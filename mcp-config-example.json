{"mcpServers": {"godot-mcp": {"command": "node", "args": ["D:/MCP/test-mcp/godot-mcp-1/build/index.js"], "env": {"DEBUG": "false", "READ_ONLY_MODE": "false", "GODOT_PATH": "D:\\Godot_v4.2.1-stable_mono_win64\\godot.exe", "USE_SIMPLIFIED_INTERFACE": "true"}}, "godot-mcp-traditional": {"command": "node", "args": ["D:/MCP/test-mcp/godot-mcp-1/build/index.js"], "env": {"DEBUG": "false", "READ_ONLY_MODE": "false", "GODOT_PATH": "D:\\Godot_v4.2.1-stable_mono_win64\\godot.exe", "USE_SIMPLIFIED_INTERFACE": "false"}}}}
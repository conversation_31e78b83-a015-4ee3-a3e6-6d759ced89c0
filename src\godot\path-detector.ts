/**
 * Godot path detection and validation
 */

import { exec } from 'child_process';
import { promisify } from 'util';
import { existsSync } from 'fs';
import { join } from 'path';
import { PathValidationCache } from '../core/types.js';
import { ConfigManager } from '../core/config.js';
import { Logger } from '../utils/logger.js';
import { PathUtils } from '../utils/path-utils.js';

const execAsync = promisify(exec);
const logger = Logger.createScopedLogger('PathDetector');

/**
 * Godot path detector class
 */
export class GodotPathDetector {
  private configManager: ConfigManager;
  private validatedPaths: PathValidationCache = new Map();
  private cachedGodotPath: string | null = null;

  constructor() {
    this.configManager = ConfigManager.getInstance();
  }

  /**
   * Detect Godot executable path
   */
  public async detectGodotPath(): Promise<string | null> {
    // Return cached path if available
    if (this.cachedGodotPath) {
      logger.debug(`Using cached Godot path: ${this.cachedGodotPath}`);
      return this.cachedGodotPath;
    }

    // Check environment variable first
    const envPath = this.configManager.getGodotPath();
    if (envPath) {
      logger.debug(`Checking environment variable path: ${envPath}`);
      if (await this.isValidGodotPath(envPath)) {
        this.cachedGodotPath = envPath;
        this.configManager.setGodotPath(envPath);
        return envPath;
      } else {
        logger.warn(`Environment variable GODOT_PATH points to invalid executable: ${envPath}`);
      }
    }

    // Try common installation paths
    const commonPaths = this.getCommonGodotPaths();
    for (const path of commonPaths) {
      logger.debug(`Checking common path: ${path}`);
      if (await this.isValidGodotPath(path)) {
        this.cachedGodotPath = path;
        this.configManager.setGodotPath(path);
        return path;
      }
    }

    // Try to find in PATH
    const pathExecutable = await this.findInPath();
    if (pathExecutable) {
      logger.debug(`Found in PATH: ${pathExecutable}`);
      if (await this.isValidGodotPath(pathExecutable)) {
        this.cachedGodotPath = pathExecutable;
        this.configManager.setGodotPath(pathExecutable);
        return pathExecutable;
      }
    }

    logger.error('Could not find a valid Godot executable');
    return null;
  }

  /**
   * Validate if a path points to a valid Godot executable
   */
  public async isValidGodotPath(path: string): Promise<boolean> {
    if (!path) {
      return false;
    }

    // Check cache first
    if (this.validatedPaths.has(path)) {
      const cached = this.validatedPaths.get(path)!;
      logger.debug(`Using cached validation result for ${path}: ${cached}`);
      return cached;
    }

    let isValid = false;

    try {
      // Check if file exists
      if (!PathUtils.pathExists(path) || !PathUtils.isFile(path)) {
        logger.debug(`Path does not exist or is not a file: ${path}`);
        this.validatedPaths.set(path, false);
        return false;
      }

      // Try to execute with --version flag
      const { stdout, stderr } = await execAsync(`"${path}" --version`, { timeout: 10000 });
      
      // Check if output contains Godot version information
      const output = stdout + stderr;
      const hasGodotVersion = /Godot Engine v\d+\.\d+/.test(output);
      
      if (hasGodotVersion) {
        logger.debug(`Valid Godot executable found: ${path}`);
        logger.debug(`Version output: ${output.trim()}`);
        isValid = true;
      } else {
        logger.debug(`Executable does not appear to be Godot: ${path}`);
        logger.debug(`Output: ${output.trim()}`);
      }
    } catch (error) {
      logger.debug(`Error validating Godot path ${path}: ${error}`);
    }

    // Cache the result
    this.validatedPaths.set(path, isValid);
    return isValid;
  }

  /**
   * Get cached Godot path
   */
  public getCachedPath(): string | null {
    return this.cachedGodotPath;
  }

  /**
   * Clear cached paths (useful for testing or when paths change)
   */
  public clearCache(): void {
    this.validatedPaths.clear();
    this.cachedGodotPath = null;
    logger.debug('Cleared path validation cache');
  }

  /**
   * Get common Godot installation paths for different platforms
   */
  private getCommonGodotPaths(): string[] {
    const platform = process.platform;
    const paths: string[] = [];

    switch (platform) {
      case 'win32':
        paths.push(
          'C:\\Program Files\\Godot\\godot.exe',
          'C:\\Program Files (x86)\\Godot\\godot.exe',
          'C:\\Godot\\godot.exe',
          join(process.env.USERPROFILE || '', 'AppData', 'Local', 'Godot', 'godot.exe'),
          join(process.env.USERPROFILE || '', 'Downloads', 'godot.exe'),
          join(process.env.USERPROFILE || '', 'Desktop', 'godot.exe')
        );
        break;

      case 'darwin':
        paths.push(
          '/Applications/Godot.app/Contents/MacOS/Godot',
          '/usr/local/bin/godot',
          '/opt/homebrew/bin/godot',
          join(process.env.HOME || '', 'Applications', 'Godot.app', 'Contents', 'MacOS', 'Godot'),
          join(process.env.HOME || '', 'Downloads', 'Godot.app', 'Contents', 'MacOS', 'Godot')
        );
        break;

      case 'linux':
        paths.push(
          '/usr/bin/godot',
          '/usr/local/bin/godot',
          '/opt/godot/godot',
          '/snap/bin/godot',
          join(process.env.HOME || '', '.local', 'bin', 'godot'),
          join(process.env.HOME || '', 'bin', 'godot'),
          join(process.env.HOME || '', 'Downloads', 'godot')
        );
        break;
    }

    // Filter out paths that don't exist to avoid unnecessary validation attempts
    return paths.filter(path => {
      try {
        return existsSync(path);
      } catch {
        return false;
      }
    });
  }

  /**
   * Try to find Godot in system PATH
   */
  private async findInPath(): Promise<string | null> {
    const commands = process.platform === 'win32' 
      ? ['godot.exe', 'godot'] 
      : ['godot'];

    for (const command of commands) {
      try {
        const whereCommand = process.platform === 'win32' ? 'where' : 'which';
        const { stdout } = await execAsync(`${whereCommand} ${command}`, { timeout: 5000 });
        const path = stdout.trim().split('\n')[0];
        
        if (path && PathUtils.pathExists(path)) {
          logger.debug(`Found ${command} in PATH: ${path}`);
          return path;
        }
      } catch (error) {
        logger.debug(`Could not find ${command} in PATH: ${error}`);
      }
    }

    return null;
  }

  /**
   * Get validation cache statistics
   */
  public getCacheStats(): { total: number; valid: number; invalid: number } {
    let valid = 0;
    let invalid = 0;

    for (const isValidPath of this.validatedPaths.values()) {
      if (isValidPath) {
        valid++;
      } else {
        invalid++;
      }
    }

    return {
      total: this.validatedPaths.size,
      valid,
      invalid
    };
  }
}

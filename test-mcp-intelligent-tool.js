#!/usr/bin/env node
/**
 * 测试MCP智能工具
 */

import { GodotMCPServer } from './build/core/server.js';

async function testMCPIntelligentTool() {
  try {
    console.log('🔧 测试MCP智能工具...\n');
    
    // 1. 启动简化接口服务器
    console.log('1. 启动简化接口MCP服务器...');
    process.env.USE_SIMPLIFIED_INTERFACE = 'true';
    process.env.DEBUG = 'true';
    
    const server = new GodotMCPServer();
    await server.start();
    
    console.log('   ✅ MCP服务器启动成功');
    
    // 2. 获取工具列表
    const stats = server.getServerStats();
    console.log(`\n2. 可用工具 (${stats.toolRegistry.totalTools}个):`);
    stats.toolRegistry.tools.forEach((tool, index) => {
      console.log(`   ${index + 1}. ${tool.name}`);
    });
    
    // 3. 测试godot_operation工具（简化接口中的智能工具）
    console.log('\n3. 测试godot_operation工具（智能场景操作）...');

    const testParams = {
      projectPath: "E:\\Github\\4.2.1\\Game-6",
      description: "获取场景 res://Scenes/Game/Island/IslandBase.tscn 的内容"
    };

    console.log('   📋 测试参数:');
    console.log(`      项目路径: ${testParams.projectPath}`);
    console.log(`      描述: ${testParams.description}`);

    console.log('\n   ⏳ 执行智能场景操作...');

    // 模拟MCP工具调用
    const toolRegistry = server.getToolRegistry();
    const intelligentTool = toolRegistry.getTool('godot_operation');

    if (!intelligentTool) {
      throw new Error('godot_operation工具未找到');
    }

    console.log('   ✅ 找到godot_operation工具（简化接口智能工具）');
    
    // 执行工具 - 通过ToolRegistry的正确方式
    const startTime = Date.now();
    const result = await toolRegistry.callTool('godot_operation', testParams);
    const duration = Date.now() - startTime;
    
    console.log(`\n   ✅ 智能场景操作完成! (耗时: ${duration}ms)`);
    
    // 4. 分析结果
    console.log('\n4. 结果分析:');
    
    if (result.success) {
      console.log('   ✅ 操作成功');
      console.log('   📊 返回数据:');
      
      if (result.data) {
        if (result.data.scenePath) {
          console.log(`      场景路径: ${result.data.scenePath}`);
        }
        if (result.data.sceneInfo) {
          console.log(`      根节点: ${result.data.sceneInfo.name} (${result.data.sceneInfo.type})`);
          console.log(`      子节点数量: ${result.data.sceneInfo.children.length}`);
          
          // 显示前几个子节点
          if (result.data.sceneInfo.children.length > 0) {
            console.log('      子节点:');
            result.data.sceneInfo.children.slice(0, 3).forEach((child, index) => {
              console.log(`        ${index + 1}. ${child.name} (${child.type})`);
            });
            if (result.data.sceneInfo.children.length > 3) {
              console.log(`        ... 还有 ${result.data.sceneInfo.children.length - 3} 个子节点`);
            }
          }
        }
        
        if (result.data.analysis) {
          console.log(`      AI分析: ${result.data.analysis}`);
        }
      }
      
      if (result.message) {
        console.log(`      消息: ${result.message}`);
      }
    } else {
      console.log('   ❌ 操作失败');
      console.log(`      错误: ${result.error}`);
    }
    
    // 5. 停止服务器
    console.log('\n5. 停止MCP服务器...');
    await server.stop();
    console.log('   ✅ MCP服务器已停止');
    
    // 6. 总结
    console.log('\n🎉 MCP智能工具测试完成!');
    
    if (result.success) {
      console.log('\n📊 测试总结:');
      console.log(`   ✅ 工具执行时间: ${duration}ms`);
      console.log('   ✅ 智能代理通信: 正常');
      console.log('   ✅ 场景数据获取: 成功');
      console.log('   ✅ MCP接口: 正常');
      
      console.log('\n🚀 您的MCP配置现在可以正常使用intelligent_scene_operation工具了!');
      console.log('\n💡 在MCP客户端中使用示例:');
      console.log('```json');
      console.log('{');
      console.log('  "tool": "godot_operation",');
      console.log('  "params": {');
      console.log(`    "projectPath": "${testParams.projectPath}",`);
      console.log(`    "description": "${testParams.description}"`);
      console.log('  }');
      console.log('}');
      console.log('```');
    } else {
      console.log('\n⚠️  测试发现问题，请检查上面的错误信息');
    }
    
  } catch (error) {
    console.error('\n❌ MCP智能工具测试失败:', error.message);
    console.error('错误详情:', error.stack);
    
    console.log('\n🔧 故障排除建议:');
    console.log('1. 确保之前的智能代理测试通过');
    console.log('2. 检查项目路径是否正确');
    console.log('3. 确保MCP服务器配置正确');
    console.log('4. 查看详细错误信息');
    
    process.exit(1);
  }
}

// 运行测试
testMCPIntelligentTool().catch(console.error);

/**
 * Parameter validation utilities for Godot MCP Server
 */

import { join, isAbsolute, resolve } from 'path';
import { existsSync, statSync } from 'fs';
import { OperationParams } from '../core/types.js';
import { Logger } from './logger.js';

const logger = Logger.createScopedLogger('Validation');

/**
 * Parameter validator class
 */
export class ParameterValidator {
  /**
   * Validate if a path exists and is a directory
   */
  public static validateProjectPath(path: string): boolean {
    if (!path || typeof path !== 'string') {
      logger.debug('Invalid project path: not a string');
      return false;
    }

    try {
      const resolvedPath = isAbsolute(path) ? path : resolve(path);
      
      if (!existsSync(resolvedPath)) {
        logger.debug(`Project path does not exist: ${resolvedPath}`);
        return false;
      }

      const stats = statSync(resolvedPath);
      if (!stats.isDirectory()) {
        logger.debug(`Project path is not a directory: ${resolvedPath}`);
        return false;
      }

      // Check for project.godot file
      const projectFile = join(resolvedPath, 'project.godot');
      if (!existsSync(projectFile)) {
        logger.debug(`No project.godot found in: ${resolvedPath}`);
        return false;
      }

      return true;
    } catch (error) {
      logger.debug(`Error validating project path: ${error}`);
      return false;
    }
  }

  /**
   * Validate scene path (relative to project)
   */
  public static validateScenePath(scenePath: string, projectPath?: string): boolean {
    if (!scenePath || typeof scenePath !== 'string') {
      logger.debug('Invalid scene path: not a string');
      return false;
    }

    // Scene path should end with .tscn
    if (!scenePath.endsWith('.tscn')) {
      logger.debug('Scene path should end with .tscn');
      return false;
    }

    // If project path is provided, check if the full path would be valid
    if (projectPath) {
      try {
        const fullPath = join(projectPath, scenePath);
        const parentDir = join(fullPath, '..');
        
        if (!existsSync(parentDir)) {
          logger.debug(`Scene parent directory does not exist: ${parentDir}`);
          return false;
        }
      } catch (error) {
        logger.debug(`Error validating scene path: ${error}`);
        return false;
      }
    }

    return true;
  }

  /**
   * Validate texture/resource path
   */
  public static validateResourcePath(resourcePath: string, projectPath?: string): boolean {
    if (!resourcePath || typeof resourcePath !== 'string') {
      logger.debug('Invalid resource path: not a string');
      return false;
    }

    // If project path is provided, check if the resource exists
    if (projectPath) {
      try {
        const fullPath = join(projectPath, resourcePath);
        
        if (!existsSync(fullPath)) {
          logger.debug(`Resource does not exist: ${fullPath}`);
          return false;
        }

        const stats = statSync(fullPath);
        if (!stats.isFile()) {
          logger.debug(`Resource path is not a file: ${fullPath}`);
          return false;
        }
      } catch (error) {
        logger.debug(`Error validating resource path: ${error}`);
        return false;
      }
    }

    return true;
  }

  /**
   * Validate node type
   */
  public static validateNodeType(nodeType: string): boolean {
    if (!nodeType || typeof nodeType !== 'string') {
      logger.debug('Invalid node type: not a string');
      return false;
    }

    // Basic validation - node type should be a valid identifier
    const validNodeTypePattern = /^[A-Za-z][A-Za-z0-9_]*$/;
    if (!validNodeTypePattern.test(nodeType)) {
      logger.debug(`Invalid node type format: ${nodeType}`);
      return false;
    }

    return true;
  }

  /**
   * Validate node name
   */
  public static validateNodeName(nodeName: string): boolean {
    if (!nodeName || typeof nodeName !== 'string') {
      logger.debug('Invalid node name: not a string');
      return false;
    }

    // Node name should not contain invalid characters
    const invalidChars = /[<>:"/\\|?*]/;
    if (invalidChars.test(nodeName)) {
      logger.debug(`Node name contains invalid characters: ${nodeName}`);
      return false;
    }

    return true;
  }

  /**
   * Convert camelCase parameters to snake_case for Godot
   */
  public static convertCamelToSnakeCase(params: OperationParams): OperationParams {
    const snakeCaseParams: OperationParams = {};
    
    for (const [key, value] of Object.entries(params)) {
      const snakeKey = key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
      snakeCaseParams[snakeKey] = value;
    }
    
    logger.debug('Converted parameters to snake_case');
    return snakeCaseParams;
  }

  /**
   * Validate required parameters
   */
  public static validateRequiredParams(params: OperationParams, required: string[]): string[] {
    const missing: string[] = [];
    
    for (const param of required) {
      if (!(param in params) || params[param] === undefined || params[param] === null) {
        missing.push(param);
      }
    }
    
    if (missing.length > 0) {
      logger.debug(`Missing required parameters: ${missing.join(', ')}`);
    }
    
    return missing;
  }

  /**
   * Sanitize file path to prevent directory traversal
   */
  public static sanitizePath(path: string): string {
    if (!path || typeof path !== 'string') {
      return '';
    }

    // Remove any attempts at directory traversal
    const sanitized = path
      .replace(/\.\./g, '') // Remove ..
      .replace(/\/+/g, '/') // Replace multiple slashes with single slash
      .replace(/\\+/g, '\\') // Replace multiple backslashes with single backslash
      .trim();

    logger.debug(`Sanitized path: ${path} -> ${sanitized}`);
    return sanitized;
  }

  /**
   * Validate operation parameters based on operation type
   */
  public static validateOperationParams(operation: string, params: OperationParams): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    switch (operation) {
      case 'create_scene':
        if (!this.validateScenePath(params.scenePath || params.scene_path)) {
          errors.push('Invalid scene path');
        }
        break;

      case 'add_node':
        if (!this.validateScenePath(params.scenePath || params.scene_path)) {
          errors.push('Invalid scene path');
        }
        if (!this.validateNodeType(params.nodeType || params.node_type)) {
          errors.push('Invalid node type');
        }
        if (!this.validateNodeName(params.nodeName || params.node_name)) {
          errors.push('Invalid node name');
        }
        break;

      case 'load_sprite':
        if (!this.validateScenePath(params.scenePath || params.scene_path)) {
          errors.push('Invalid scene path');
        }
        if (!this.validateResourcePath(params.texturePath || params.texture_path)) {
          errors.push('Invalid texture path');
        }
        break;

      default:
        // For unknown operations, just validate common parameters
        if (params.scenePath || params.scene_path) {
          if (!this.validateScenePath(params.scenePath || params.scene_path)) {
            errors.push('Invalid scene path');
          }
        }
        break;
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }
}

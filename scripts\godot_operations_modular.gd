#!/usr/bin/env -S godot --headless --script
# Modular Godot Operations Entry Point
# This is the new modular version of godot_operations.gd
# Maintains 100% backward compatibility with the original interface

extends SceneTree

# Import all required modules
const GodotLogger = preload("res://scripts/core/logger.gd")
const GodotArgumentParser = preload("res://scripts/core/argument_parser.gd")
const GodotOperationRouter = preload("res://scripts/core/operation_router.gd")
const GodotClassUtils = preload("res://scripts/utils/class_utils.gd")
const GodotFileUtils = preload("res://scripts/utils/file_utils.gd")
const GodotSceneOperations = preload("res://scripts/operations/scene_operations.gd")
const GodotResourceOperations = preload("res://scripts/operations/resource_operations.gd")

# Main entry point
func _init():
	# Parse command line arguments
	var parsed_args = GodotArgumentParser.parse_arguments()
	
	if not parsed_args:
		GodotLogger.log_error("Failed to parse command line arguments")
		quit(1)
		return
	
	# Log environment information in debug mode
	if parsed_args.debug_mode:
		GodotLogger.log_debug_environment()
	
	# Validate operation is supported
	if not GodotOperationRouter.is_operation_supported(parsed_args.operation):
		GodotLogger.log_error("Unknown operation: " + parsed_args.operation)
		_print_supported_operations()
		quit(1)
		return
	
	# Execute the operation
	var success = GodotOperationRouter.route_operation(parsed_args.operation, parsed_args.params)
	
	if success:
		GodotLogger.log_success("Operation completed successfully")
		quit(0)
	else:
		GodotLogger.log_error("Operation failed")
		quit(1)

# Print supported operations for help
func _print_supported_operations():
	GodotLogger.log_info("Supported operations:")
	var operations = GodotOperationRouter.get_supported_operations()
	for operation in operations:
		GodotLogger.log_info("  - " + operation)

# Legacy function stubs for backward compatibility
# These functions maintain the exact same interface as the original

func log_debug(message):
	GodotLogger.log_debug(message)

func log_info(message):
	GodotLogger.log_info(message)

func log_error(message):
	GodotLogger.log_error(message)

func get_script_by_name(name_of_class):
	return GodotClassUtils.get_script_by_name(name_of_class)

func instantiate_class(name_of_class):
	return GodotClassUtils.instantiate_class(name_of_class)

func create_scene(params):
	return GodotSceneOperations.create_scene(params)

func add_node(params):
	return GodotSceneOperations.add_node(params)

func load_sprite(params):
	return GodotSceneOperations.load_sprite(params)

func save_scene(params):
	return GodotSceneOperations.save_scene(params)

func export_mesh_library(params):
	return GodotResourceOperations.export_mesh_library(params)

func get_uid(params):
	return GodotResourceOperations.get_uid(params)

func resave_resources(params):
	return GodotResourceOperations.resave_resources(params)

func find_files(path, extension):
	return GodotFileUtils.find_files(path, extension)

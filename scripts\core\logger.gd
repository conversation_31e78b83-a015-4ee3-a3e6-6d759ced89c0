#!/usr/bin/env -S godot --headless --script
# GDScript Logger Module
# Provides centralized logging functionality for Godot operations

class_name GodotLogger

# Debug mode flag - can be set externally
static var debug_mode: bool = false

# Initialize debug mode from command line arguments
static func init_debug_mode():
	var args = OS.get_cmdline_args()
	debug_mode = "--debug-godot" in args

# Debug logging - only prints if debug mode is enabled
static func log_debug(message: String):
	if debug_mode:
		print("[DEBUG] " + message)

# Info logging - always prints
static func log_info(message: String):
	print("[INFO] " + message)

# Error logging - prints to stderr
static func log_error(message: String):
	printerr("[ERROR] " + message)

# Warning logging
static func log_warn(message: String):
	print("[WARN] " + message)

# Success logging
static func log_success(message: String):
	print("[SUCCESS] " + message)

# Scoped logger for specific operations
static func log_operation_start(operation: String):
	log_info("Starting operation: " + operation)

static func log_operation_complete(operation: String):
	log_success("Completed operation: " + operation)

static func log_operation_error(operation: String, error: String):
	log_error("Failed operation '" + operation + "': " + error)

# Debug environment information
static func log_debug_environment():
	if not debug_mode:
		return
		
	log_debug("=== Environment Debug Info ===")
	
	# Project paths
	var project_res_path = "res://"
	var project_user_path = "user://"
	var global_res_path = ProjectSettings.globalize_path(project_res_path)
	var global_user_path = ProjectSettings.globalize_path(project_user_path)
	
	log_debug("Project paths:")
	log_debug("- res:// path: " + project_res_path)
	log_debug("- user:// path: " + project_user_path)
	log_debug("- Globalized res:// path: " + global_res_path)
	log_debug("- Globalized user:// path: " + global_user_path)
	
	# Environment variables
	log_debug("Environment variables:")
	var env_vars = ["PATH", "HOME", "USER", "TEMP", "GODOT_PATH"]
	for env_var in env_vars:
		if OS.has_environment(env_var):
			log_debug("  " + env_var + " = " + OS.get_environment(env_var))
	
	log_debug("=== End Environment Debug Info ===")

# Log command line arguments for debugging
static func log_debug_args():
	if not debug_mode:
		return
		
	var args = OS.get_cmdline_args()
	log_debug("Command line arguments (" + str(args.size()) + "):")
	for i in range(args.size()):
		log_debug("  [" + str(i) + "] " + args[i])

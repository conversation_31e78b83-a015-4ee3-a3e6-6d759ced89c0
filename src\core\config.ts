/**
 * Configuration management for Godot MCP Server
 */

import { GodotServerConfig, DEFAULT_CONFIG } from './types.js';

/**
 * Configuration manager class
 */
export class ConfigManager {
  private static instance: ConfigManager;
  private config: GodotServerConfig;

  private constructor() {
    this.config = this.loadConfig();
  }

  /**
   * Get singleton instance
   */
  public static getInstance(): ConfigManager {
    if (!ConfigManager.instance) {
      ConfigManager.instance = new ConfigManager();
    }
    return ConfigManager.instance;
  }

  /**
   * Load configuration from environment variables and defaults
   */
  private loadConfig(): GodotServerConfig {
    const config: GodotServerConfig = {
      ...DEFAULT_CONFIG,
      godotPath: process.env.GODOT_PATH,
      debugMode: process.env.DEBUG === 'true',
      godotDebugMode: process.env.GODOT_DEBUG !== 'false', // Default to true
      strictPathValidation: process.env.STRICT_PATH_VALIDATION === 'true',
    };

    return config;
  }

  /**
   * Get current configuration
   */
  public getConfig(): GodotServerConfig {
    return { ...this.config };
  }

  /**
   * Update configuration
   */
  public updateConfig(updates: Partial<GodotServerConfig>): void {
    this.config = { ...this.config, ...updates };
  }

  /**
   * Validate configuration
   */
  public validateConfig(config?: GodotServerConfig): boolean {
    const configToValidate = config || this.config;
    
    // Basic validation
    if (typeof configToValidate.debugMode !== 'boolean') {
      return false;
    }
    
    if (typeof configToValidate.godotDebugMode !== 'boolean') {
      return false;
    }
    
    if (typeof configToValidate.strictPathValidation !== 'boolean') {
      return false;
    }
    
    if (configToValidate.godotPath && typeof configToValidate.godotPath !== 'string') {
      return false;
    }
    
    return true;
  }

  /**
   * Get debug mode status
   */
  public isDebugMode(): boolean {
    return this.config.debugMode || false;
  }

  /**
   * Get Godot debug mode status
   */
  public isGodotDebugMode(): boolean {
    return this.config.godotDebugMode !== false; // Default to true
  }

  /**
   * Get strict path validation status
   */
  public isStrictPathValidation(): boolean {
    return this.config.strictPathValidation || false;
  }

  /**
   * Get Godot path
   */
  public getGodotPath(): string | undefined {
    return this.config.godotPath;
  }

  /**
   * Set Godot path
   */
  public setGodotPath(path: string): void {
    this.config.godotPath = path;
  }

  /**
   * Reset configuration to defaults
   */
  public resetToDefaults(): void {
    this.config = this.loadConfig();
  }

  /**
   * Export configuration for logging/debugging
   */
  public exportConfig(): Record<string, any> {
    return {
      debugMode: this.config.debugMode,
      godotDebugMode: this.config.godotDebugMode,
      strictPathValidation: this.config.strictPathValidation,
      hasGodotPath: !!this.config.godotPath,
      godotPath: this.config.godotPath ? '[SET]' : '[NOT SET]',
    };
  }
}

/**
 * 智能工具 - 高级AI驱动的Godot操作工具
 * 这些工具使用内置AI模型来理解复杂请求并执行相应的Godot操作
 */

import { MCPTool, OperationParams, OperationResult } from '../core/types.js';
import { IntelligentAgentController } from '../agent/intelligent-agent-controller.js';
import { Logger } from '../utils/logger.js';

const logger = Logger.createScopedLogger('IntelligentTools');

/**
 * 智能场景操作工具
 * 理解自然语言描述的场景操作需求，自动执行相应的Godot操作
 */
async function intelligentSceneOperation(
  this: { agentController: IntelligentAgentController }, 
  params: OperationParams
): Promise<OperationResult> {
  try {
    const { projectPath, description, scenePath, operation, nodeData } = params;

    logger.info(`执行智能场景操作: ${description}`);

    // 确保代理正在运行
    if (!this.agentController.getAgentStatus().isRunning) {
      const startResult = await this.agentController.startAgent(projectPath);
      if (!startResult.success) {
        return startResult;
      }
    }

    // 执行场景操作
    const result = await this.agentController.executeSceneOperation({
      projectPath,
      description,
      scenePath,
      operation,
      nodeData
    });

    logger.info(`智能场景操作完成: ${result.success ? '成功' : '失败'}`);
    return result;

  } catch (error) {
    logger.error('智能场景操作失败:', error);
    return {
      success: false,
      error: `智能场景操作失败: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

/**
 * 查询项目状态工具
 * 智能分析项目状态，提供详细的项目信息和建议
 */
async function queryProjectState(
  this: { agentController: IntelligentAgentController }, 
  params: OperationParams
): Promise<OperationResult> {
  try {
    const { projectPath, query, includeScenes = true, includeResources = true } = params;

    logger.info(`查询项目状态: ${query || '全面查询'}`);

    // 确保代理正在运行
    if (!this.agentController.getAgentStatus().isRunning) {
      const startResult = await this.agentController.startAgent(projectPath);
      if (!startResult.success) {
        return startResult;
      }
    }

    // 执行项目查询
    const result = await this.agentController.queryProjectState({
      projectPath,
      query,
      includeScenes,
      includeResources
    });

    logger.info(`项目状态查询完成: ${result.success ? '成功' : '失败'}`);
    return result;

  } catch (error) {
    logger.error('项目状态查询失败:', error);
    return {
      success: false,
      error: `项目状态查询失败: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

/**
 * 管理项目工具
 * 执行项目级别的管理操作，如创建、配置、优化等
 */
async function manageProject(
  this: { agentController: IntelligentAgentController }, 
  params: OperationParams
): Promise<OperationResult> {
  try {
    const { projectPath, action, configuration, targetPath } = params;

    logger.info(`管理项目: ${action}`);

    // 确保代理正在运行
    if (!this.agentController.getAgentStatus().isRunning) {
      const startResult = await this.agentController.startAgent(projectPath);
      if (!startResult.success) {
        return startResult;
      }
    }

    // 执行项目管理
    const result = await this.agentController.manageProject({
      projectPath,
      action,
      configuration,
      targetPath
    });

    logger.info(`项目管理完成: ${result.success ? '成功' : '失败'}`);
    return result;

  } catch (error) {
    logger.error('项目管理失败:', error);
    return {
      success: false,
      error: `项目管理失败: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

/**
 * 调试和运行工具
 * 智能调试和运行Godot项目，提供详细的调试信息
 */
async function debugAndRun(
  this: { agentController: IntelligentAgentController }, 
  params: OperationParams
): Promise<OperationResult> {
  try {
    const { projectPath, scene, debugMode = true, captureOutput = true } = params;

    logger.info(`调试运行项目: ${scene || 'main scene'}`);

    // 确保代理正在运行
    if (!this.agentController.getAgentStatus().isRunning) {
      const startResult = await this.agentController.startAgent(projectPath);
      if (!startResult.success) {
        return startResult;
      }
    }

    // 执行调试运行
    const result = await this.agentController.debugAndRun({
      projectPath,
      scene,
      debugMode,
      captureOutput
    });

    logger.info(`调试运行完成: ${result.success ? '成功' : '失败'}`);
    return result;

  } catch (error) {
    logger.error('调试运行失败:', error);
    return {
      success: false,
      error: `调试运行失败: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

/**
 * 智能工具定义
 */
export const intelligentTools: MCPTool[] = [
  {
    name: 'intelligent_scene_operation',
    description: '智能场景操作 - 使用AI理解自然语言描述的场景操作需求，自动执行相应的Godot操作',
    inputSchema: {
      type: 'object',
      properties: {
        projectPath: {
          type: 'string',
          description: 'Godot项目的路径'
        },
        description: {
          type: 'string',
          description: '操作描述，用自然语言描述想要执行的场景操作'
        },
        scenePath: {
          type: 'string',
          description: '场景文件路径（相对于项目根目录）'
        },
        operation: {
          type: 'string',
          description: '具体操作类型（如create_scene, modify_node, add_node等）',
          enum: ['create_scene', 'modify_node', 'add_node', 'delete_node', 'query_scene']
        },
        nodeData: {
          type: 'object',
          description: '节点相关数据',
          properties: {
            rootType: { type: 'string', description: '根节点类型' },
            nodeType: { type: 'string', description: '节点类型' },
            nodeName: { type: 'string', description: '节点名称' },
            properties: { type: 'object', description: '节点属性' }
          }
        }
      },
      required: ['projectPath', 'description']
    },
    handler: intelligentSceneOperation
  },
  {
    name: 'query_project_state',
    description: '查询项目状态 - 智能分析项目状态，提供详细的项目信息和建议',
    inputSchema: {
      type: 'object',
      properties: {
        projectPath: {
          type: 'string',
          description: 'Godot项目的路径'
        },
        query: {
          type: 'string',
          description: '查询内容描述，如"项目结构"、"场景列表"、"资源使用情况"等'
        },
        includeScenes: {
          type: 'boolean',
          description: '是否包含场景信息',
          default: true
        },
        includeResources: {
          type: 'boolean',
          description: '是否包含资源信息',
          default: true
        }
      },
      required: ['projectPath']
    },
    handler: queryProjectState
  },
  {
    name: 'manage_project',
    description: '管理项目 - 执行项目级别的管理操作，如创建、配置、优化等',
    inputSchema: {
      type: 'object',
      properties: {
        projectPath: {
          type: 'string',
          description: 'Godot项目的路径'
        },
        action: {
          type: 'string',
          description: '管理操作类型',
          enum: ['create', 'configure', 'optimize', 'backup', 'export']
        },
        configuration: {
          type: 'object',
          description: '配置参数'
        },
        targetPath: {
          type: 'string',
          description: '目标路径（用于导出、备份等操作）'
        }
      },
      required: ['projectPath', 'action']
    },
    handler: manageProject
  },
  {
    name: 'debug_and_run',
    description: '调试和运行 - 智能调试和运行Godot项目，提供详细的调试信息',
    inputSchema: {
      type: 'object',
      properties: {
        projectPath: {
          type: 'string',
          description: 'Godot项目的路径'
        },
        scene: {
          type: 'string',
          description: '要运行的场景路径（可选，默认为主场景）'
        },
        debugMode: {
          type: 'boolean',
          description: '是否启用调试模式',
          default: true
        },
        captureOutput: {
          type: 'boolean',
          description: '是否捕获输出',
          default: true
        }
      },
      required: ['projectPath']
    },
    handler: debugAndRun
  }
];

export { intelligentSceneOperation, queryProjectState, manageProject, debugAndRun };

/**
 * Tool registry for Godot MCP Server
 */

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { ListToolsRequestSchema, CallToolRequestSchema } from '@modelcontextprotocol/sdk/types.js';
import { MCPTool, OperationParams, OperationResult } from '../core/types.js';
import { Logger } from '../utils/logger.js';
import { GodotOperationExecutor } from '../godot/operation-executor.js';

// Import tool definitions
import { editorTools } from './editor-tools.js';
import { projectTools } from './project-tools.js';
import { sceneTools } from './scene-tools.js';
import { resourceTools } from './resource-tools.js';

const logger = Logger.createScopedLogger('ToolRegistry');

/**
 * Tool registry class
 */
export class ToolRegistry {
  private server: Server;
  private operationExecutor: GodotOperationExecutor;
  private tools: Map<string, MCPTool> = new Map();

  constructor(server: Server, operationExecutor: GodotOperationExecutor) {
    this.server = server;
    this.operationExecutor = operationExecutor;
  }

  /**
   * Register all tools with the MCP server
   */
  public registerAllTools(): void {
    logger.info('Registering all MCP tools');

    // Collect all tools
    const allTools = [
      ...editorTools,
      ...projectTools,
      ...sceneTools,
      ...resourceTools,
    ];

    // Register each tool
    for (const tool of allTools) {
      this.registerTool(tool);
    }

    // Set up MCP server handlers
    this.setupServerHandlers();

    logger.info(`Registered ${this.tools.size} tools successfully`);
  }

  /**
   * Register a single tool
   */
  private registerTool(tool: MCPTool): void {
    if (this.tools.has(tool.name)) {
      logger.warn(`Tool ${tool.name} is already registered, skipping`);
      return;
    }

    this.tools.set(tool.name, tool);
    logger.debug(`Registered tool: ${tool.name}`);
  }

  /**
   * Set up MCP server request handlers
   */
  private setupServerHandlers(): void {
    // Handle list tools request
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      const tools = Array.from(this.tools.values()).map(tool => ({
        name: tool.name,
        description: tool.description,
        inputSchema: tool.inputSchema,
      }));

      logger.debug(`Listing ${tools.length} available tools`);
      return { tools };
    });

    // Handle call tool request
    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;
      
      logger.info(`Tool called: ${name}`);
      logger.debug(`Tool arguments: ${JSON.stringify(args)}`);

      const tool = this.tools.get(name);
      if (!tool) {
        const error = `Unknown tool: ${name}`;
        logger.error(error);
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify({
                success: false,
                error,
              }),
            },
          ],
        };
      }

      try {
        // Execute the tool
        const result = await this.executeTool(tool, args || {});
        
        logger.info(`Tool ${name} completed ${result.success ? 'successfully' : 'with errors'}`);
        
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify(result),
            },
          ],
        };
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        logger.error(`Tool ${name} execution failed: ${errorMessage}`);
        
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify({
                success: false,
                error: errorMessage,
              }),
            },
          ],
        };
      }
    });
  }

  /**
   * Execute a tool with the given parameters
   */
  private async executeTool(tool: MCPTool, params: OperationParams): Promise<OperationResult> {
    try {
      // Create a context object for the tool handler
      const context = {
        operationExecutor: this.operationExecutor,
        logger: Logger.createScopedLogger(`Tool:${tool.name}`),
      };

      // Execute the tool handler with context
      const result = await tool.handler.call(context, params);
      
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logger.error(`Tool handler error: ${errorMessage}`);
      
      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  /**
   * Get list of registered tool names
   */
  public getRegisteredToolNames(): string[] {
    return Array.from(this.tools.keys());
  }

  /**
   * Get tool by name
   */
  public getTool(name: string): MCPTool | undefined {
    return this.tools.get(name);
  }

  /**
   * Check if a tool is registered
   */
  public hasToolRegistered(name: string): boolean {
    return this.tools.has(name);
  }

  /**
   * Get registry statistics
   */
  public getRegistryStats(): {
    totalTools: number;
    editorTools: number;
    projectTools: number;
    sceneTools: number;
    resourceTools: number;
  } {
    const stats = {
      totalTools: this.tools.size,
      editorTools: 0,
      projectTools: 0,
      sceneTools: 0,
      resourceTools: 0,
    };

    for (const tool of this.tools.values()) {
      if (editorTools.some(t => t.name === tool.name)) {
        stats.editorTools++;
      } else if (projectTools.some(t => t.name === tool.name)) {
        stats.projectTools++;
      } else if (sceneTools.some(t => t.name === tool.name)) {
        stats.sceneTools++;
      } else if (resourceTools.some(t => t.name === tool.name)) {
        stats.resourceTools++;
      }
    }

    return stats;
  }
}

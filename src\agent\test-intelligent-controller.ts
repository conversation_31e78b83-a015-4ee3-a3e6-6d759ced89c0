#!/usr/bin/env node
/**
 * 测试智能代理控制器
 */

import { IntelligentAgentController } from './intelligent-agent-controller.js';
import { GodotPathDetector } from '../godot/path-detector.js';
import { Logger } from '../utils/logger.js';

const logger = Logger.createScopedLogger('TestIntelligentController');

async function testIntelligentController() {
  try {
    logger.info('开始测试智能代理控制器...');

    // 创建路径检测器
    const pathDetector = new GodotPathDetector();
    
    // 创建智能代理控制器
    const controller = new IntelligentAgentController(pathDetector);
    
    // 测试项目路径
    const projectPath = process.cwd();
    
    logger.info(`使用项目路径: ${projectPath}`);

    // 启动代理
    logger.info('启动智能代理...');
    const startResult = await controller.startAgent(projectPath);
    logger.info('启动结果:', startResult);

    if (startResult.success) {
      // 测试场景操作
      logger.info('测试场景操作...');
      const sceneResult = await controller.executeSceneOperation({
        projectPath,
        description: '创建一个新的2D场景',
        scenePath: 'test_intelligent_scene.tscn',
        operation: 'create_scene',
        nodeData: { rootType: 'Node2D' }
      });
      logger.info('场景操作结果:', sceneResult);

      // 测试项目查询
      logger.info('测试项目查询...');
      const queryResult = await controller.queryProjectState({
        projectPath,
        query: '项目状态概览'
      });
      logger.info('项目查询结果:', queryResult);

      // 获取代理状态
      const status = controller.getAgentStatus();
      logger.info('代理状态:', status);

      // 停止代理
      logger.info('停止智能代理...');
      const stopResult = await controller.stopAgent();
      logger.info('停止结果:', stopResult);
    }

    logger.info('智能代理控制器测试完成');

  } catch (error) {
    logger.error('测试失败:', error);
  }
}

// 运行测试
if (import.meta.url === `file://${process.argv[1]}`) {
  testIntelligentController().catch(console.error);
}

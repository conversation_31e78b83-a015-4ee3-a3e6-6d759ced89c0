#!/usr/bin/env -S godot --headless --script
# Godot Agent Inspector - 动态查询节点属性和方法的概念验证
extends SceneTree

func _init():
	# 创建一个测试场景来演示查询功能
	create_test_scene()
	
	# 演示各种查询功能
	demonstrate_inspection_capabilities()
	
	quit()

func create_test_scene():
	print("=== 创建测试场景 ===")
	
	# 创建根节点
	var root = Node2D.new()
	root.name = "TestRoot"
	
	# 添加一个Sprite2D节点
	var sprite = Sprite2D.new()
	sprite.name = "TestSprite"
	sprite.position = Vector2(100, 50)
	root.add_child(sprite)
	
	# 添加一个Label节点
	var label = Label.new()
	label.name = "TestLabel"
	label.text = "Hello Godot Agent!"
	label.position = Vector2(200, 100)
	root.add_child(label)
	
	# 添加一个CollisionShape2D节点
	var collision = CollisionShape2D.new()
	collision.name = "TestCollision"
	var shape = RectangleShape2D.new()
	shape.size = Vector2(64, 64)
	collision.shape = shape
	root.add_child(collision)
	
	# 将根节点添加到场景树
	root.set_owner(root)
	get_root().add_child(root)
	print("测试场景创建完成")

func demonstrate_inspection_capabilities():
	print("\n=== 演示动态查询功能 ===")

	var root_node = get_root().get_node("TestRoot")
	if not root_node:
		print("错误：没有找到测试根节点")
		return

	# 1. 查询场景树结构
	print("\n1. 场景树结构：")
	print_scene_tree(root_node, 0)
	
	# 2. 查询特定节点的详细信息
	var sprite_node = root_node.get_node("TestSprite")
	if sprite_node:
		print("\n2. Sprite2D节点详细信息：")
		inspect_node_details(sprite_node)

	# 3. 查询节点的可用方法
	print("\n3. Sprite2D节点可用方法：")
	inspect_node_methods(sprite_node)

	# 4. 演示属性修改
	print("\n4. 演示属性修改：")
	demonstrate_property_modification(sprite_node)

func print_scene_tree(node: Node, depth: int):
	var indent = "  ".repeat(depth)
	var node_info = "%s%s (%s)" % [indent, node.name, node.get_class()]
	
	# 添加位置信息（如果是Node2D）
	if node is Node2D:
		node_info += " - Position: %s" % str(node.position)
	
	print(node_info)
	
	# 递归打印子节点
	for child in node.get_children():
		print_scene_tree(child, depth + 1)

func inspect_node_details(node: Node) -> Dictionary:
	var details = {}
	
	print("节点类型: %s" % node.get_class())
	print("节点名称: %s" % node.name)
	print("节点路径: %s" % node.get_path())
	
	# 获取所有属性
	var properties = node.get_property_list()
	print("可用属性数量: %d" % properties.size())
	
	var editable_properties = []
	for prop in properties:
		# 过滤掉只读和内部属性
		if prop.usage & PROPERTY_USAGE_EDITOR and not (prop.usage & PROPERTY_USAGE_READ_ONLY):
			var prop_info = {
				"name": prop.name,
				"type": prop.type,
				"hint": prop.hint,
				"hint_string": prop.hint_string,
				"current_value": node.get(prop.name)
			}
			editable_properties.append(prop_info)
			
			print("  - %s (%s): %s" % [prop.name, get_type_name(prop.type), str(node.get(prop.name))])
	
	details["properties"] = editable_properties
	return details

func inspect_node_methods(node: Node) -> Array:
	var methods = node.get_method_list()
	var useful_methods = []
	
	print("可用方法数量: %d" % methods.size())
	
	# 过滤出有用的方法（排除内部方法）
	for method in methods:
		var method_name = method.name
		if not method_name.begins_with("_") and not method_name in ["get_class", "get_instance_id"]:
			useful_methods.append(method)
			
			var args_info = ""
			if method.args.size() > 0:
				var arg_names = []
				for arg in method.args:
					arg_names.append("%s: %s" % [arg.name, get_type_name(arg.type)])
				args_info = "(%s)" % ", ".join(arg_names)
			else:
				args_info = "()"
			
			var return_type = method.get("return_type", TYPE_NIL)
			print("  - %s%s -> %s" % [method_name, args_info, get_type_name(return_type)])
	
	return useful_methods

func demonstrate_property_modification(node: Node):
	if not node:
		return
	
	print("修改前的属性值：")
	if node is Node2D:
		print("  position: %s" % str(node.position))
		print("  rotation: %s" % str(node.rotation))
		print("  scale: %s" % str(node.scale))
	
	# 修改属性
	if node is Node2D:
		node.position = Vector2(150, 75)
		node.rotation = deg_to_rad(45)
		node.scale = Vector2(1.5, 1.5)
	
	print("修改后的属性值：")
	if node is Node2D:
		print("  position: %s" % str(node.position))
		print("  rotation: %s" % str(node.rotation))
		print("  scale: %s" % str(node.scale))

func get_type_name(type_id: int) -> String:
	match type_id:
		TYPE_NIL: return "nil"
		TYPE_BOOL: return "bool"
		TYPE_INT: return "int"
		TYPE_FLOAT: return "float"
		TYPE_STRING: return "String"
		TYPE_VECTOR2: return "Vector2"
		TYPE_VECTOR2I: return "Vector2i"
		TYPE_RECT2: return "Rect2"
		TYPE_RECT2I: return "Rect2i"
		TYPE_VECTOR3: return "Vector3"
		TYPE_VECTOR3I: return "Vector3i"
		TYPE_TRANSFORM2D: return "Transform2D"
		TYPE_VECTOR4: return "Vector4"
		TYPE_VECTOR4I: return "Vector4i"
		TYPE_PLANE: return "Plane"
		TYPE_QUATERNION: return "Quaternion"
		TYPE_AABB: return "AABB"
		TYPE_BASIS: return "Basis"
		TYPE_TRANSFORM3D: return "Transform3D"
		TYPE_PROJECTION: return "Projection"
		TYPE_COLOR: return "Color"
		TYPE_STRING_NAME: return "StringName"
		TYPE_NODE_PATH: return "NodePath"
		TYPE_RID: return "RID"
		TYPE_OBJECT: return "Object"
		TYPE_CALLABLE: return "Callable"
		TYPE_SIGNAL: return "Signal"
		TYPE_DICTIONARY: return "Dictionary"
		TYPE_ARRAY: return "Array"
		TYPE_PACKED_BYTE_ARRAY: return "PackedByteArray"
		TYPE_PACKED_INT32_ARRAY: return "PackedInt32Array"
		TYPE_PACKED_INT64_ARRAY: return "PackedInt64Array"
		TYPE_PACKED_FLOAT32_ARRAY: return "PackedFloat32Array"
		TYPE_PACKED_FLOAT64_ARRAY: return "PackedFloat64Array"
		TYPE_PACKED_STRING_ARRAY: return "PackedStringArray"
		TYPE_PACKED_VECTOR2_ARRAY: return "PackedVector2Array"
		TYPE_PACKED_VECTOR3_ARRAY: return "PackedVector3Array"
		TYPE_PACKED_COLOR_ARRAY: return "PackedColorArray"
		_: return "Unknown"

#!/usr/bin/env -S godot --headless --script
# Godot代理测试版本 - 验证基本通信功能
extends SceneTree

var debug_mode: bool = false

func _init():
	var args = OS.get_cmdline_args()
	debug_mode = "--debug-godot" in args
	
	log_debug("=== Godot持久化代理测试启动 ===")
	
	# 输出启动信号
	print_json_response({
		"type": "agent_started",
		"message": "Godot持久化代理已启动",
		"timestamp": Time.get_unix_time_from_system(),
		"version": Engine.get_version_info()
	})
	
	# 测试基本功能
	test_basic_functionality()
	
	# 输出完成信号
	print_json_response({
		"type": "test_completed",
		"message": "基本功能测试完成"
	})
	
	quit()

func test_basic_functionality():
	"""测试基本功能"""
	log_debug("开始基本功能测试")
	
	# 测试1: 创建场景
	test_create_scene()
	
	# 测试2: 查询场景
	test_query_scene()
	
	# 测试3: 修改节点
	test_modify_node()

func test_create_scene():
	"""测试创建场景"""
	log_debug("测试创建场景")
	
	# 创建根节点
	var root = Node2D.new()
	root.name = "TestRoot"
	
	# 添加子节点
	var sprite = Sprite2D.new()
	sprite.name = "TestSprite"
	sprite.position = Vector2(100, 50)
	root.add_child(sprite)
	
	var label = Label.new()
	label.name = "TestLabel"
	label.text = "Hello Agent!"
	root.add_child(label)
	
	# 分析场景
	var scene_info = analyze_node_recursive(root, "")
	
	print_json_response({
		"type": "scene_created",
		"data": scene_info
	})
	
	# 清理
	root.queue_free()

func test_query_scene():
	"""测试查询场景功能"""
	log_debug("测试查询场景功能")
	
	# 创建测试场景
	var root = Node2D.new()
	root.name = "QueryTestRoot"
	
	# 添加不同类型的节点
	var sprite = Sprite2D.new()
	sprite.name = "QuerySprite"
	sprite.position = Vector2(200, 100)
	sprite.scale = Vector2(1.5, 1.5)
	root.add_child(sprite)
	
	var collision = CollisionShape2D.new()
	collision.name = "QueryCollision"
	var shape = RectangleShape2D.new()
	shape.size = Vector2(64, 64)
	collision.shape = shape
	root.add_child(collision)
	
	# 查询节点属性
	var sprite_properties = get_node_properties(sprite)
	var collision_properties = get_node_properties(collision)
	
	print_json_response({
		"type": "scene_queried",
		"data": {
			"sprite_properties": sprite_properties,
			"collision_properties": collision_properties
		}
	})
	
	# 清理
	root.queue_free()

func test_modify_node():
	"""测试修改节点功能"""
	log_debug("测试修改节点功能")
	
	# 创建测试节点
	var sprite = Sprite2D.new()
	sprite.name = "ModifyTestSprite"
	sprite.position = Vector2(50, 25)
	sprite.rotation = 0
	sprite.scale = Vector2(1, 1)
	
	# 记录修改前的状态
	var before_state = {
		"position": sprite.position,
		"rotation": sprite.rotation,
		"scale": sprite.scale
	}
	
	# 执行修改
	sprite.position = Vector2(150, 75)
	sprite.rotation = deg_to_rad(45)
	sprite.scale = Vector2(2, 2)
	
	# 记录修改后的状态
	var after_state = {
		"position": sprite.position,
		"rotation": sprite.rotation,
		"scale": sprite.scale
	}
	
	print_json_response({
		"type": "node_modified",
		"data": {
			"before": before_state,
			"after": after_state
		}
	})
	
	# 清理
	sprite.queue_free()

func analyze_node_recursive(node: Node, path_prefix: String) -> Dictionary:
	"""递归分析节点"""
	var node_path = path_prefix + "/" + node.name if path_prefix != "" else node.name
	
	var node_info = {
		"name": node.name,
		"type": node.get_class(),
		"path": node_path,
		"properties": get_node_properties(node),
		"children": []
	}
	
	# 分析子节点
	for child in node.get_children():
		node_info.children.append(analyze_node_recursive(child, node_path))
	
	return node_info

func get_node_properties(node: Node) -> Dictionary:
	"""获取节点的可编辑属性"""
	var properties = {}
	var prop_list = node.get_property_list()
	
	var important_props = ["position", "rotation", "scale", "visible", "modulate", "text", "texture"]
	
	for prop in prop_list:
		if prop.usage & PROPERTY_USAGE_EDITOR and not (prop.usage & PROPERTY_USAGE_READ_ONLY):
			# 只包含重要属性以减少输出
			if prop.name in important_props:
				properties[prop.name] = {
					"type": get_type_name(prop.type),
					"value": var_to_str(node.get(prop.name))
				}
	
	return properties

func get_type_name(type_id: int) -> String:
	"""获取类型名称"""
	match type_id:
		TYPE_NIL: return "nil"
		TYPE_BOOL: return "bool"
		TYPE_INT: return "int"
		TYPE_FLOAT: return "float"
		TYPE_STRING: return "String"
		TYPE_VECTOR2: return "Vector2"
		TYPE_VECTOR3: return "Vector3"
		TYPE_COLOR: return "Color"
		TYPE_OBJECT: return "Object"
		_: return "Unknown"

func print_json_response(data: Dictionary):
	"""输出JSON响应"""
	var json_string = JSON.stringify(data)
	print(json_string)

func log_debug(message: String):
	"""调试日志"""
	if debug_mode:
		print_json_response({
			"type": "debug",
			"message": message
		})

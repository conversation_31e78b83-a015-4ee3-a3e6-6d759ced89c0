# Godot MCP 模块化架构设计

## 概述

本文档描述了Godot MCP项目的模块化重构方案，旨在提高代码的可维护性、可扩展性和可测试性。

## 设计原则

1. **单一职责原则**: 每个模块只负责一个特定的功能领域
2. **松耦合**: 模块间通过明确定义的接口进行交互
3. **高内聚**: 相关功能集中在同一模块内
4. **向后兼容**: 保持现有MCP工具API的完全兼容性
5. **可测试性**: 每个模块都可以独立进行单元测试

## TypeScript模块架构

### 核心模块 (core/)

#### server.ts
```typescript
export interface MCPServerConfig {
  name: string;
  version: string;
  capabilities: ServerCapabilities;
}

export class GodotMCPServer {
  constructor(config: MCPServerConfig);
  async start(): Promise<void>;
  async stop(): Promise<void>;
  registerTool(tool: MCPTool): void;
}
```

#### config.ts
```typescript
export interface GodotServerConfig {
  godotPath?: string;
  debugMode?: boolean;
  godotDebugMode?: boolean;
  strictPathValidation?: boolean;
}

export class ConfigManager {
  static load(): GodotServerConfig;
  static validate(config: GodotServerConfig): boolean;
}
```

#### types.ts
```typescript
export interface GodotProcess {
  process: any;
  output: string[];
  errors: string[];
}

export interface OperationParams {
  [key: string]: any;
}

export interface OperationResult {
  success: boolean;
  data?: any;
  error?: string;
}
```

### Godot模块 (godot/)

#### path-detector.ts
```typescript
export class GodotPathDetector {
  async detectGodotPath(): Promise<string | null>;
  async isValidGodotPath(path: string): Promise<boolean>;
  getCachedPath(): string | null;
}
```

#### process-manager.ts
```typescript
export class GodotProcessManager {
  async startProcess(command: string[]): Promise<GodotProcess>;
  async stopProcess(process: GodotProcess): Promise<void>;
  getActiveProcess(): GodotProcess | null;
}
```

#### operation-executor.ts
```typescript
export class GodotOperationExecutor {
  constructor(pathDetector: GodotPathDetector, processManager: GodotProcessManager);
  async executeOperation(operation: string, params: OperationParams, projectPath: string): Promise<OperationResult>;
}
```

### 工具模块 (tools/)

#### index.ts
```typescript
export class ToolRegistry {
  registerAllTools(server: GodotMCPServer): void;
  private registerEditorTools(server: GodotMCPServer): void;
  private registerProjectTools(server: GodotMCPServer): void;
  private registerSceneTools(server: GodotMCPServer): void;
  private registerResourceTools(server: GodotMCPServer): void;
}
```

#### editor-tools.ts
```typescript
export const editorTools: MCPTool[] = [
  {
    name: 'launch_editor',
    description: 'Launch Godot editor for a specific project',
    handler: async (params) => { /* implementation */ }
  },
  {
    name: 'get_godot_version',
    description: 'Get the installed Godot version',
    handler: async (params) => { /* implementation */ }
  }
];
```

### 工具模块 (utils/)

#### logger.ts
```typescript
export enum LogLevel {
  DEBUG = 'DEBUG',
  INFO = 'INFO',
  WARN = 'WARN',
  ERROR = 'ERROR'
}

export class Logger {
  static debug(message: string): void;
  static info(message: string): void;
  static warn(message: string): void;
  static error(message: string): void;
}
```

#### validation.ts
```typescript
export class ParameterValidator {
  static validateProjectPath(path: string): boolean;
  static validateScenePath(path: string): boolean;
  static convertCamelToSnakeCase(params: any): any;
}
```

## GDScript模块架构

### 核心模块 (core/)

#### operation_router.gd
```gdscript
class_name OperationRouter

static func route_operation(operation: String, params: Dictionary) -> void:
    match operation:
        "create_scene":
            SceneManager.create_scene(params)
        "add_node":
            NodeManager.add_node(params)
        # ... 其他操作路由
```

#### logger.gd
```gdscript
class_name GodotLogger

enum LogLevel { DEBUG, INFO, WARN, ERROR }

static var debug_mode: bool = false

static func debug(message: String) -> void
static func info(message: String) -> void
static func error(message: String) -> void
```

#### class_factory.gd
```gdscript
class_name ClassFactory

static func instantiate_class(class_name: String) -> Node:
    # 统一的类实例化逻辑
```

### 场景模块 (scene/)

#### scene_manager.gd
```gdscript
class_name SceneManager

static func create_scene(params: Dictionary) -> void
static func save_scene(params: Dictionary) -> void
static func load_scene(scene_path: String) -> PackedScene
```

#### node_manager.gd
```gdscript
class_name NodeManager

static func add_node(params: Dictionary) -> void
static func find_node_by_path(root: Node, path: String) -> Node
static func set_node_properties(node: Node, properties: Dictionary) -> void
```

## 模块依赖关系

```mermaid
graph TD
    A[index.ts] --> B[GodotMCPServer]
    B --> C[ToolRegistry]
    C --> D[EditorTools]
    C --> E[ProjectTools]
    C --> F[SceneTools]
    C --> G[ResourceTools]
    
    D --> H[GodotOperationExecutor]
    E --> H
    F --> H
    G --> H
    
    H --> I[GodotPathDetector]
    H --> J[GodotProcessManager]
    
    K[Logger] --> B
    K --> H
    L[ParameterValidator] --> D
    L --> E
    L --> F
    L --> G
```

## 重构实施计划

1. **创建新的模块文件结构**
2. **逐步迁移现有功能到新模块**
3. **更新导入和依赖关系**
4. **添加类型定义和文档**
5. **编写单元测试**
6. **验证功能完整性**

## 向后兼容性保证

- 所有现有的MCP工具名称和参数保持不变
- API响应格式保持一致
- 错误处理行为保持相同
- 配置选项保持兼容

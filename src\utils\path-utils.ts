/**
 * Path utilities for Godot MCP Server
 */

import { join, resolve, isAbsolute, dirname, basename, extname } from 'path';
import { existsSync, statSync } from 'fs';
import { Logger } from './logger.js';

const logger = Logger.createScopedLogger('PathUtils');

/**
 * Path utilities class
 */
export class PathUtils {
  /**
   * Resolve a path relative to a base directory
   */
  public static resolvePath(path: string, basePath?: string): string {
    if (isAbsolute(path)) {
      return resolve(path);
    }
    
    if (basePath) {
      return resolve(basePath, path);
    }
    
    return resolve(path);
  }

  /**
   * Check if a path exists
   */
  public static pathExists(path: string): boolean {
    try {
      return existsSync(path);
    } catch (error) {
      logger.debug(`Error checking path existence: ${error}`);
      return false;
    }
  }

  /**
   * Check if a path is a directory
   */
  public static isDirectory(path: string): boolean {
    try {
      if (!existsSync(path)) {
        return false;
      }
      return statSync(path).isDirectory();
    } catch (error) {
      logger.debug(`Error checking if path is directory: ${error}`);
      return false;
    }
  }

  /**
   * Check if a path is a file
   */
  public static isFile(path: string): boolean {
    try {
      if (!existsSync(path)) {
        return false;
      }
      return statSync(path).isFile();
    } catch (error) {
      logger.debug(`Error checking if path is file: ${error}`);
      return false;
    }
  }

  /**
   * Get the directory name of a path
   */
  public static getDirectory(path: string): string {
    return dirname(path);
  }

  /**
   * Get the filename from a path
   */
  public static getFilename(path: string): string {
    return basename(path);
  }

  /**
   * Get the file extension
   */
  public static getExtension(path: string): string {
    return extname(path);
  }

  /**
   * Get the filename without extension
   */
  public static getFilenameWithoutExtension(path: string): string {
    const filename = basename(path);
    const ext = extname(filename);
    return filename.slice(0, -ext.length);
  }

  /**
   * Join multiple path segments
   */
  public static joinPaths(...segments: string[]): string {
    return join(...segments);
  }

  /**
   * Normalize path separators for the current platform
   */
  public static normalizePath(path: string): string {
    return resolve(path);
  }

  /**
   * Convert path to use forward slashes (for Godot compatibility)
   */
  public static toGodotPath(path: string): string {
    return path.replace(/\\/g, '/');
  }

  /**
   * Convert relative path to be relative to project root
   */
  public static toProjectRelativePath(path: string, projectPath: string): string {
    const absolutePath = this.resolvePath(path, projectPath);
    const absoluteProjectPath = this.resolvePath(projectPath);
    
    if (absolutePath.startsWith(absoluteProjectPath)) {
      const relativePath = absolutePath.slice(absoluteProjectPath.length);
      return relativePath.startsWith('/') || relativePath.startsWith('\\') 
        ? relativePath.slice(1) 
        : relativePath;
    }
    
    return path;
  }

  /**
   * Ensure a path has a specific extension
   */
  public static ensureExtension(path: string, extension: string): string {
    const currentExt = extname(path);
    const targetExt = extension.startsWith('.') ? extension : `.${extension}`;
    
    if (currentExt.toLowerCase() === targetExt.toLowerCase()) {
      return path;
    }
    
    return path + targetExt;
  }

  /**
   * Check if a path is within a parent directory (security check)
   */
  public static isWithinDirectory(path: string, parentDir: string): boolean {
    const absolutePath = this.resolvePath(path);
    const absoluteParentDir = this.resolvePath(parentDir);
    
    return absolutePath.startsWith(absoluteParentDir);
  }

  /**
   * Create a safe path by removing dangerous components
   */
  public static createSafePath(path: string): string {
    return path
      .replace(/\.\./g, '') // Remove parent directory references
      .replace(/[<>:"|?*]/g, '') // Remove invalid filename characters
      .replace(/\/+/g, '/') // Replace multiple slashes
      .replace(/\\+/g, '\\') // Replace multiple backslashes
      .trim();
  }

  /**
   * Get common path patterns for Godot projects
   */
  public static getGodotPaths() {
    return {
      projectFile: 'project.godot',
      scenesDir: 'scenes',
      scriptsDir: 'scripts',
      assetsDir: 'assets',
      texturesDir: 'textures',
      modelsDir: 'models',
      audioDir: 'audio',
      fontsDir: 'fonts',
    };
  }

  /**
   * Check if a path looks like a Godot project directory
   */
  public static looksLikeGodotProject(path: string): boolean {
    const projectFile = this.joinPaths(path, this.getGodotPaths().projectFile);
    return this.pathExists(projectFile);
  }

  /**
   * Find Godot project root from a given path
   */
  public static findGodotProjectRoot(startPath: string): string | null {
    let currentPath = this.resolvePath(startPath);
    
    while (currentPath !== dirname(currentPath)) {
      if (this.looksLikeGodotProject(currentPath)) {
        return currentPath;
      }
      currentPath = dirname(currentPath);
    }
    
    return null;
  }

  /**
   * Validate that a scene path is valid for Godot
   */
  public static isValidGodotScenePath(path: string): boolean {
    // Must end with .tscn
    if (!path.endsWith('.tscn')) {
      return false;
    }
    
    // Must not contain invalid characters
    const invalidChars = /[<>:"|?*]/;
    if (invalidChars.test(path)) {
      return false;
    }
    
    // Must not start with / or contain ..
    if (path.startsWith('/') || path.includes('..')) {
      return false;
    }
    
    return true;
  }

  /**
   * Validate that a resource path is valid for Godot
   */
  public static isValidGodotResourcePath(path: string): boolean {
    // Must not contain invalid characters
    const invalidChars = /[<>:"|?*]/;
    if (invalidChars.test(path)) {
      return false;
    }
    
    // Must not start with / or contain ..
    if (path.startsWith('/') || path.includes('..')) {
      return false;
    }
    
    return true;
  }
}

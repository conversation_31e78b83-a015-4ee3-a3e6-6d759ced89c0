#!/usr/bin/env node
/**
 * 测试AI集成功能
 */

import { GodotMCPServer } from './build/core/server.js';
import { AIConfigManager } from './build/ai/ai-config.js';
import { TaskPlanner } from './build/ai/task-planner.js';

async function testAIIntegration() {
  try {
    console.log('🤖 开始测试AI集成功能...\n');
    
    // 1. 测试AI配置
    console.log('1. 测试AI配置管理器...');
    const aiConfig = AIConfigManager.getInstance();
    const configSummary = aiConfig.getConfigSummary();
    console.log('AI配置摘要:', JSON.stringify(configSummary, null, 2));
    console.log(`AI功能状态: ${aiConfig.isAIEnabled() ? '✅ 已启用' : '❌ 未启用'}\n`);
    
    // 2. 测试任务规划器
    console.log('2. 测试AI任务规划器...');
    const taskPlanner = new TaskPlanner();
    
    // 检查AI健康状态
    console.log('检查AI服务健康状态...');
    const isHealthy = await taskPlanner.checkAIHealth();
    console.log(`AI服务状态: ${isHealthy ? '✅ 正常' : '❌ 异常'}\n`);
    
    if (aiConfig.isAIEnabled() && isHealthy) {
      // 3. 测试任务规划
      console.log('3. 测试AI任务规划...');
      const planningContext = {
        projectPath: process.cwd(),
        currentScenes: ['main.tscn', 'player.tscn'],
        recentOperations: ['创建场景', '添加节点']
      };
      
      const testRequest = '创建一个2D平台游戏的主角色场景，包含精灵、碰撞体和移动脚本';
      console.log(`测试请求: ${testRequest}`);
      
      try {
        const taskPlan = await taskPlanner.planTask(testRequest, planningContext);
        console.log('✅ 任务规划成功!');
        console.log(`任务类型: ${taskPlan.task_type}`);
        console.log(`任务描述: ${taskPlan.description}`);
        console.log(`步骤数量: ${taskPlan.steps.length}`);
        console.log(`预期结果: ${taskPlan.expected_outcome}`);
        
        if (taskPlan.steps.length > 0) {
          console.log('\n任务步骤:');
          taskPlan.steps.forEach((step, index) => {
            console.log(`  ${index + 1}. ${step.action} (优先级: ${step.priority})`);
            console.log(`     原因: ${step.reason}`);
          });
        }
        
        if (taskPlan.risks.length > 0) {
          console.log('\n潜在风险:');
          taskPlan.risks.forEach((risk, index) => {
            console.log(`  ${index + 1}. ${risk}`);
          });
        }
        
      } catch (planError) {
        console.log('❌ 任务规划失败:', planError.message);
      }
    } else {
      console.log('⚠️  跳过AI功能测试 - AI服务不可用');
      console.log('提示: 请在.env文件中配置AI模型参数');
    }
    
    // 4. 测试MCP服务器与AI集成
    console.log('\n4. 测试MCP服务器AI集成...');
    const server = new GodotMCPServer();
    const stats = server.getServerStats();
    
    console.log('服务器统计信息:');
    console.log(`- 工具总数: ${stats.toolRegistry.totalTools}`);
    console.log(`- 智能工具: ${stats.toolRegistry.intelligentTools || 0}`);
    console.log(`- 配置状态: ${stats.config.hasGodotPath ? '✅' : '❌'} Godot路径`);
    
    console.log('\n🎉 AI集成测试完成!');
    
    // 5. 显示使用建议
    console.log('\n📋 使用建议:');
    if (!aiConfig.isAIEnabled()) {
      console.log('1. 复制 .env.example 到 .env');
      console.log('2. 配置 OPENAI_API_KEY 和其他AI参数');
      console.log('3. 重新运行测试以验证AI功能');
    } else {
      console.log('1. AI功能已就绪，可以使用智能工具');
      console.log('2. 使用 intelligent_scene_operation 工具进行智能场景操作');
      console.log('3. 使用自然语言描述您的需求，AI将自动规划和执行');
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    console.error('错误详情:', error.stack);
  }
}

// 运行测试
testAIIntegration().catch(console.error);

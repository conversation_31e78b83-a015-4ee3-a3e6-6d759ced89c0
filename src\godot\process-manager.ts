/**
 * Godot process management
 */

import { spawn, ChildProcess } from 'child_process';
import { GodotProcess } from '../core/types.js';
import { ConfigManager } from '../core/config.js';
import { Logger } from '../utils/logger.js';

const logger = Logger.createScopedLogger('ProcessManager');

/**
 * Godot process manager class
 */
export class GodotProcessManager {
  private configManager: ConfigManager;
  private activeProcess: GodotProcess | null = null;

  constructor() {
    this.configManager = ConfigManager.getInstance();
  }

  /**
   * Start a new Godot process
   */
  public async startProcess(command: string[], options: {
    cwd?: string;
    timeout?: number;
  } = {}): Promise<GodotProcess> {
    return new Promise((resolve, reject) => {
      logger.debug(`Starting Godot process: ${command.join(' ')}`);
      
      const process = spawn(command[0], command.slice(1), {
        cwd: options.cwd,
        stdio: ['pipe', 'pipe', 'pipe'],
        shell: false,
      });

      const godotProcess: GodotProcess = {
        process,
        output: [],
        errors: [],
      };

      // Set up timeout if specified
      let timeoutId: NodeJS.Timeout | null = null;
      if (options.timeout) {
        timeoutId = setTimeout(() => {
          logger.warn(`Process timeout after ${options.timeout}ms, killing process`);
          this.killProcess(process);
          reject(new Error(`Process timeout after ${options.timeout}ms`));
        }, options.timeout);
      }

      // Handle stdout
      process.stdout?.on('data', (data: Buffer) => {
        const output = data.toString();
        godotProcess.output.push(output);
        if (this.configManager.isDebugMode()) {
          logger.debug(`Process stdout: ${output.trim()}`);
        }
      });

      // Handle stderr
      process.stderr?.on('data', (data: Buffer) => {
        const error = data.toString();
        godotProcess.errors.push(error);
        if (this.configManager.isDebugMode()) {
          logger.debug(`Process stderr: ${error.trim()}`);
        }
      });

      // Handle process exit
      process.on('exit', (code, signal) => {
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
        
        logger.debug(`Process exited with code ${code}, signal ${signal}`);
        
        if (this.activeProcess === godotProcess) {
          this.activeProcess = null;
        }
        
        resolve(godotProcess);
      });

      // Handle process errors
      process.on('error', (error) => {
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
        
        logger.error(`Process error: ${error.message}`);
        
        if (this.activeProcess === godotProcess) {
          this.activeProcess = null;
        }
        
        reject(error);
      });

      // Store as active process
      this.activeProcess = godotProcess;
    });
  }

  /**
   * Stop a Godot process
   */
  public async stopProcess(godotProcess?: GodotProcess): Promise<void> {
    const processToStop = godotProcess || this.activeProcess;
    
    if (!processToStop) {
      logger.debug('No process to stop');
      return;
    }

    return new Promise((resolve) => {
      const process = processToStop.process;
      
      if (!process || process.killed) {
        logger.debug('Process already killed or does not exist');
        resolve();
        return;
      }

      logger.debug(`Stopping process with PID: ${process.pid}`);

      // Set up timeout for forceful kill
      const forceKillTimeout = setTimeout(() => {
        logger.warn('Process did not terminate gracefully, force killing');
        this.killProcess(process);
      }, 5000);

      // Handle process exit
      process.on('exit', () => {
        clearTimeout(forceKillTimeout);
        logger.debug('Process stopped successfully');
        
        if (this.activeProcess === processToStop) {
          this.activeProcess = null;
        }
        
        resolve();
      });

      // Try graceful termination first
      try {
        process.kill('SIGTERM');
      } catch (error) {
        logger.debug(`Error sending SIGTERM: ${error}`);
        this.killProcess(process);
        clearTimeout(forceKillTimeout);
        resolve();
      }
    });
  }

  /**
   * Force kill a process
   */
  private killProcess(process: ChildProcess): void {
    try {
      if (process && !process.killed) {
        process.kill('SIGKILL');
        logger.debug('Process force killed');
      }
    } catch (error) {
      logger.debug(`Error force killing process: ${error}`);
    }
  }

  /**
   * Get the currently active process
   */
  public getActiveProcess(): GodotProcess | null {
    return this.activeProcess;
  }

  /**
   * Check if there's an active process
   */
  public hasActiveProcess(): boolean {
    return this.activeProcess !== null && 
           this.activeProcess.process && 
           !this.activeProcess.process.killed;
  }

  /**
   * Wait for a process to complete
   */
  public async waitForProcess(godotProcess: GodotProcess, timeout?: number): Promise<void> {
    return new Promise((resolve, reject) => {
      const process = godotProcess.process;
      
      if (!process || process.killed) {
        resolve();
        return;
      }

      // Set up timeout if specified
      let timeoutId: NodeJS.Timeout | null = null;
      if (timeout) {
        timeoutId = setTimeout(() => {
          reject(new Error(`Wait timeout after ${timeout}ms`));
        }, timeout);
      }

      // Wait for process to exit
      process.on('exit', () => {
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
        resolve();
      });

      process.on('error', (error: Error) => {
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
        reject(error);
      });
    });
  }

  /**
   * Get combined output from a process
   */
  public getProcessOutput(godotProcess: GodotProcess): {
    stdout: string;
    stderr: string;
    combined: string;
  } {
    const stdout = godotProcess.output.join('');
    const stderr = godotProcess.errors.join('');
    const combined = stdout + stderr;

    return { stdout, stderr, combined };
  }

  /**
   * Clean up all processes
   */
  public async cleanup(): Promise<void> {
    logger.debug('Cleaning up all processes');
    
    if (this.activeProcess) {
      await this.stopProcess(this.activeProcess);
    }
    
    logger.debug('Process cleanup completed');
  }

  /**
   * Get process statistics
   */
  public getProcessStats(): {
    hasActiveProcess: boolean;
    activeProcessPid?: number;
    outputLines: number;
    errorLines: number;
  } {
    const stats = {
      hasActiveProcess: this.hasActiveProcess(),
      outputLines: 0,
      errorLines: 0,
    };

    if (this.activeProcess) {
      stats.outputLines = this.activeProcess.output.length;
      stats.errorLines = this.activeProcess.errors.length;
      
      if (this.activeProcess.process && !this.activeProcess.process.killed) {
        (stats as any).activeProcessPid = this.activeProcess.process.pid;
      }
    }

    return stats;
  }
}

#!/usr/bin/env -S godot --headless --script
# GDScript Resource Operations Module
# Handles resource-related operations: export mesh library, get UID, resave resources

class_name GodotResourceOperations

# Export a scene as a MeshLibrary resource
static func export_mesh_library(params: Dictionary) -> bool:
	# Validate required parameters
	if not GodotArgumentParser.validate_required_params(params, ["scene_path", "output_path"]):
		return false
	
	var scene_path = params.scene_path
	var output_path = params.output_path
	var mesh_item_names = params.get("mesh_item_names", [])
	
	GodotLogger.log_info("Exporting MeshLibrary from scene: " + scene_path)
	
	# Normalize paths
	var full_scene_path = GodotArgumentParser.normalize_scene_path(scene_path)
	var full_output_path = GodotArgumentParser.normalize_scene_path(output_path)
	
	GodotLogger.log_debug("Full scene path: " + full_scene_path)
	GodotLogger.log_debug("Full output path: " + full_output_path)
	
	# Validate scene file exists
	if not GodotArgumentParser.validate_file_exists(full_scene_path, "scene"):
		return false
	
	# Load and instantiate the scene
	var scene = load(full_scene_path)
	if not scene:
		GodotLogger.log_error("Failed to load scene: " + full_scene_path)
		return false
	
	var scene_root = scene.instantiate()
	if not scene_root:
		GodotLogger.log_error("Failed to instantiate scene")
		return false
	
	GodotLogger.log_debug("Scene loaded and instantiated successfully")
	
	# Create MeshLibrary and process nodes
	var mesh_library = MeshLibrary.new()
	var item_count = _process_mesh_nodes(scene_root, mesh_library, mesh_item_names)
	
	if item_count == 0:
		GodotLogger.log_error("No valid meshes found in the scene")
		return false
	
	# Ensure output directory exists
	var output_dir = full_output_path.get_base_dir()
	if not GodotFileUtils.ensure_directory_exists(output_dir):
		return false
	
	# Save the mesh library
	GodotLogger.log_debug("Saving MeshLibrary to: " + full_output_path)
	var save_error = ResourceSaver.save(mesh_library, full_output_path)
	
	if save_error == OK:
		# Verify the file was created
		if FileAccess.file_exists(full_output_path):
			GodotLogger.log_success("MeshLibrary exported successfully with " + str(item_count) + " items to: " + output_path)
			var absolute_path = GodotArgumentParser.get_absolute_path(full_output_path)
			GodotLogger.log_debug("Absolute file path: " + absolute_path)
			return true
		else:
			GodotLogger.log_error("File reported as saved but does not exist")
			return false
	else:
		GodotLogger.log_error("Failed to save MeshLibrary: " + str(save_error))
		return false

# Get UID for a specific file
static func get_uid(params: Dictionary) -> bool:
	# Validate required parameters
	if not GodotArgumentParser.validate_required_params(params, ["file_path"]):
		return false
	
	var file_path = params.file_path
	GodotLogger.log_info("Getting UID for file: " + file_path)
	
	# Normalize file path
	var full_file_path = GodotArgumentParser.normalize_scene_path(file_path)
	GodotLogger.log_debug("Full file path: " + full_file_path)
	
	var absolute_path = GodotArgumentParser.get_absolute_path(full_file_path)
	GodotLogger.log_debug("Absolute file path: " + absolute_path)
	
	# Validate file exists
	if not GodotArgumentParser.validate_file_exists(full_file_path, "file"):
		return false
	
	# Check for UID file
	var uid_path = full_file_path + ".uid"
	GodotLogger.log_debug("UID file path: " + uid_path)
	
	var uid_file = FileAccess.open(uid_path, FileAccess.READ)
	var result = {}
	
	if uid_file:
		# Read UID content
		var uid_content = uid_file.get_as_text()
		uid_file.close()
		
		result = {
			"file": full_file_path,
			"absolutePath": absolute_path,
			"uid": uid_content.strip_edges(),
			"exists": true
		}
		GodotLogger.log_debug("UID found: " + uid_content.strip_edges())
	else:
		# UID file doesn't exist
		result = {
			"file": full_file_path,
			"absolutePath": absolute_path,
			"exists": false,
			"message": "UID file does not exist for this file. Use resave_resources to generate UIDs."
		}
		GodotLogger.log_debug("UID file does not exist")
	
	# Output result as JSON
	var json_result = JSON.stringify(result)
	GodotLogger.log_debug("UID result: " + json_result)
	print(json_result)
	
	return true

# Resave all resources to update UID references
static func resave_resources(params: Dictionary) -> bool:
	GodotLogger.log_info("Resaving all resources to update UID references...")
	
	# Get project path (default to res://)
	var project_path = params.get("project_path", "res://")
	if not project_path.begins_with("res://"):
		project_path = "res://" + project_path
	if not project_path.ends_with("/"):
		project_path += "/"
	
	GodotLogger.log_debug("Using project path: " + project_path)
	
	# Process scenes
	var scene_stats = _resave_scenes(project_path)
	
	# Process scripts and shaders
	var script_stats = _resave_scripts_and_shaders(project_path)
	
	# Log summary
	GodotLogger.log_debug("=== Resave Summary ===")
	GodotLogger.log_debug("Scenes processed: " + str(scene_stats.total))
	GodotLogger.log_debug("Scenes successfully saved: " + str(scene_stats.success))
	GodotLogger.log_debug("Scenes with errors: " + str(scene_stats.errors))
	GodotLogger.log_debug("Scripts/shaders missing UIDs: " + str(script_stats.missing))
	GodotLogger.log_debug("UIDs successfully generated: " + str(script_stats.generated))
	
	GodotLogger.log_success("Resave operation complete")
	return true

# Private helper functions

# Process mesh nodes and add them to MeshLibrary
static func _process_mesh_nodes(scene_root: Node, mesh_library: MeshLibrary, mesh_item_names: Array) -> int:
	var use_specific_items = mesh_item_names.size() > 0
	var item_id = 0

	if use_specific_items:
		GodotLogger.log_debug("Using specific mesh items: " + str(mesh_item_names))
	else:
		GodotLogger.log_debug("Using all mesh items in the scene")

	GodotLogger.log_debug("Processing child nodes...")

	for child in scene_root.get_children():
		GodotLogger.log_debug("Checking child node: " + child.name)

		# Skip if not using all items and this item is not in the list
		if use_specific_items and not (child.name in mesh_item_names):
			GodotLogger.log_debug("Skipping node " + child.name + " (not in specified items list)")
			continue

		# Find MeshInstance3D
		var mesh_instance = _find_mesh_instance(child)

		if mesh_instance and mesh_instance.mesh:
			GodotLogger.log_debug("Adding mesh: " + child.name)

			# Add mesh to library
			mesh_library.create_item(item_id)
			mesh_library.set_item_name(item_id, child.name)
			mesh_library.set_item_mesh(item_id, mesh_instance.mesh)

			# Add collision shape if available
			_add_collision_shape(mesh_library, item_id, child)

			# Add preview
			mesh_library.set_item_preview(item_id, mesh_instance.mesh)
			GodotLogger.log_debug("Added mesh to library with ID: " + str(item_id))

			item_id += 1
		else:
			GodotLogger.log_debug("Node " + child.name + " has no valid mesh")

	GodotLogger.log_debug("Processed " + str(item_id) + " meshes")
	return item_id

# Find MeshInstance3D in node or its descendants
static func _find_mesh_instance(node: Node) -> MeshInstance3D:
	if node is MeshInstance3D:
		GodotLogger.log_debug("Node " + node.name + " is a MeshInstance3D")
		return node

	# Search descendants
	GodotLogger.log_debug("Searching for MeshInstance3D in descendants of " + node.name)
	for child in node.get_children():
		if child is MeshInstance3D:
			GodotLogger.log_debug("Found MeshInstance3D in descendant: " + child.name)
			return child

	return null

# Add collision shape to mesh library item
static func _add_collision_shape(mesh_library: MeshLibrary, item_id: int, node: Node):
	var collision_added = false

	for child in node.get_children():
		if child is CollisionShape3D and child.shape:
			mesh_library.set_item_shapes(item_id, [child.shape])
			GodotLogger.log_debug("Added collision shape from: " + child.name)
			collision_added = true
			break

	if not collision_added:
		GodotLogger.log_debug("No collision shape found for mesh: " + node.name)

# Resave all scene files
static func _resave_scenes(project_path: String) -> Dictionary:
	GodotLogger.log_debug("Searching for scene files in: " + project_path)
	var scenes = GodotFileUtils.find_files(project_path, "tscn")
	GodotLogger.log_debug("Found " + str(scenes.size()) + " scenes")

	var success_count = 0
	var error_count = 0

	for scene_path in scenes:
		GodotLogger.log_debug("Processing scene: " + scene_path)

		if not FileAccess.file_exists(scene_path):
			GodotLogger.log_error("Scene file does not exist: " + scene_path)
			error_count += 1
			continue

		var scene = load(scene_path)
		if scene:
			var save_error = ResourceSaver.save(scene, scene_path)
			if save_error == OK:
				success_count += 1
				GodotLogger.log_debug("Scene saved successfully: " + scene_path)
			else:
				error_count += 1
				GodotLogger.log_error("Failed to save scene: " + scene_path + ", error: " + str(save_error))
		else:
			error_count += 1
			GodotLogger.log_error("Failed to load scene: " + scene_path)

	return {
		"total": scenes.size(),
		"success": success_count,
		"errors": error_count
	}

# Resave scripts and shaders to generate UIDs
static func _resave_scripts_and_shaders(project_path: String) -> Dictionary:
	GodotLogger.log_debug("Searching for script and shader files in: " + project_path)

	var scripts = []
	scripts.append_array(GodotFileUtils.find_files(project_path, "gd"))
	scripts.append_array(GodotFileUtils.find_files(project_path, "shader"))
	scripts.append_array(GodotFileUtils.find_files(project_path, "gdshader"))

	GodotLogger.log_debug("Found " + str(scripts.size()) + " scripts/shaders")

	var missing_uids = 0
	var generated_uids = 0

	for script_path in scripts:
		GodotLogger.log_debug("Checking UID for: " + script_path)
		var uid_path = script_path + ".uid"

		if not FileAccess.file_exists(uid_path):
			missing_uids += 1
			GodotLogger.log_debug("Missing UID file for: " + script_path + ", generating...")

			# Force save to generate UID
			var resource = load(script_path)
			if resource:
				var save_error = ResourceSaver.save(resource, script_path)
				if save_error == OK:
					generated_uids += 1
					GodotLogger.log_debug("Generated UID for: " + script_path)
				else:
					GodotLogger.log_error("Failed to generate UID for: " + script_path + ", error: " + str(save_error))
			else:
				GodotLogger.log_error("Failed to load resource: " + script_path)
		else:
			GodotLogger.log_debug("UID file already exists for: " + script_path)

	return {
		"missing": missing_uids,
		"generated": generated_uids
	}

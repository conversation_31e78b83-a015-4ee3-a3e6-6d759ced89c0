/**
 * 简化的MCP工具接口
 * 将14个传统工具简化为3个高级智能工具，保持向后兼容性
 */

import { MCPTool, OperationParams, OperationResult } from '../core/types.js';
import { IntelligentAgentController } from '../agent/intelligent-agent-controller.js';
import { Logger } from '../utils/logger.js';

const logger = Logger.createScopedLogger('SimplifiedTools');

/**
 * 统一的Godot操作工具
 * 替代所有传统的场景、项目、编辑器和资源工具
 */
async function godotOperation(
  this: { agentController: IntelligentAgentController }, 
  params: OperationParams
): Promise<OperationResult> {
  try {
    const { projectPath, description, operation, ...otherParams } = params;

    logger.info(`执行Godot操作: ${description || operation}`);
    logger.info(`this对象类型: ${typeof this}`);
    logger.info(`this对象键: ${Object.keys(this)}`);
    logger.info(`agentController状态: ${this.agentController ? '已初始化' : '未初始化'}`);

    if (!this.agentController) {
      throw new Error('智能代理控制器未初始化');
    }

    // 确保代理正在运行
    if (!this.agentController.getAgentStatus().isRunning) {
      const startResult = await this.agentController.startAgent(projectPath);
      if (!startResult.success) {
        return startResult;
      }
    }

    // 使用AI增强的智能操作
    const result = await this.agentController.executeIntelligentOperation({
      projectPath,
      description: description || `执行${operation}操作`,
      context: {
        operation,
        ...otherParams
      }
    });

    logger.info(`Godot操作完成: ${result.success ? '成功' : '失败'}`);
    return result;

  } catch (error) {
    logger.error('Godot操作失败:', error);
    return {
      success: false,
      error: `Godot操作失败: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

/**
 * 项目查询和分析工具
 * 替代所有查询相关的工具
 */
async function projectQuery(
  this: { agentController: IntelligentAgentController }, 
  params: OperationParams
): Promise<OperationResult> {
  try {
    const { projectPath, query, description, ...otherParams } = params;

    logger.info(`执行项目查询: ${description || query}`);

    // 确保代理正在运行
    if (!this.agentController.getAgentStatus().isRunning) {
      const startResult = await this.agentController.startAgent(projectPath);
      if (!startResult.success) {
        return startResult;
      }
    }

    // 使用AI增强的智能操作
    const result = await this.agentController.executeIntelligentOperation({
      projectPath,
      description: description || `查询${query}`,
      context: {
        queryType: 'project_query',
        query,
        ...otherParams
      }
    });

    logger.info(`项目查询完成: ${result.success ? '成功' : '失败'}`);
    return result;

  } catch (error) {
    logger.error('项目查询失败:', error);
    return {
      success: false,
      error: `项目查询失败: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

/**
 * 项目管理工具
 * 替代所有管理和配置相关的工具
 */
async function projectManagement(
  this: { agentController: IntelligentAgentController }, 
  params: OperationParams
): Promise<OperationResult> {
  try {
    const { projectPath, action, description, ...otherParams } = params;

    logger.info(`执行项目管理: ${description || action}`);

    // 确保代理正在运行
    if (!this.agentController.getAgentStatus().isRunning) {
      const startResult = await this.agentController.startAgent(projectPath);
      if (!startResult.success) {
        return startResult;
      }
    }

    // 使用AI增强的智能操作
    const result = await this.agentController.executeIntelligentOperation({
      projectPath,
      description: description || `执行${action}管理操作`,
      context: {
        managementType: 'project_management',
        action,
        ...otherParams
      }
    });

    logger.info(`项目管理完成: ${result.success ? '成功' : '失败'}`);
    return result;

  } catch (error) {
    logger.error('项目管理失败:', error);
    return {
      success: false,
      error: `项目管理失败: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

/**
 * 简化工具定义
 * 3个高级智能工具替代14个传统工具
 */
export const simplifiedTools: MCPTool[] = [
  {
    name: 'godot_operation',
    description: '统一的Godot操作工具 - 使用AI理解并执行各种Godot操作，包括场景创建/修改、节点操作、资源管理、编辑器控制等',
    inputSchema: {
      type: 'object',
      properties: {
        projectPath: {
          type: 'string',
          description: 'Godot项目的路径'
        },
        description: {
          type: 'string',
          description: '操作描述，用自然语言描述想要执行的操作'
        },
        operation: {
          type: 'string',
          description: '具体操作类型（可选，用于向后兼容）',
          enum: [
            // 场景操作
            'create_scene', 'add_node', 'load_sprite', 'save_scene',
            // 编辑器操作
            'launch_editor', 'run_project', 'get_debug_output', 'stop_project', 'get_godot_version',
            // 资源操作
            'export_mesh_library', 'get_uid', 'update_project_uids'
          ]
        },
        // 通用参数，支持所有传统工具的参数
        scenePath: { type: 'string', description: '场景文件路径' },
        nodeName: { type: 'string', description: '节点名称' },
        nodeType: { type: 'string', description: '节点类型' },
        parentNodePath: { type: 'string', description: '父节点路径' },
        properties: { type: 'object', description: '节点属性' },
        texturePath: { type: 'string', description: '纹理文件路径' },
        rootNodeType: { type: 'string', description: '根节点类型' },
        scene: { type: 'string', description: '要运行的场景' },
        outputPath: { type: 'string', description: '输出路径' },
        filePath: { type: 'string', description: '文件路径' },
        newPath: { type: 'string', description: '新路径' }
      },
      required: ['projectPath']
    },
    handler: godotOperation,
  },
  {
    name: 'project_query',
    description: '项目查询和分析工具 - 智能分析项目状态、获取项目信息、列出项目等查询操作',
    inputSchema: {
      type: 'object',
      properties: {
        projectPath: {
          type: 'string',
          description: 'Godot项目的路径'
        },
        description: {
          type: 'string',
          description: '查询描述，用自然语言描述想要查询的信息'
        },
        query: {
          type: 'string',
          description: '查询类型（可选，用于向后兼容）',
          enum: ['list_projects', 'get_project_info', 'project_status', 'scene_list', 'resource_list']
        },
        // 查询参数
        directory: { type: 'string', description: '搜索目录' },
        recursive: { type: 'boolean', description: '是否递归搜索' },
        includeScenes: { type: 'boolean', description: '是否包含场景信息' },
        includeResources: { type: 'boolean', description: '是否包含资源信息' }
      },
      required: ['projectPath']
    },
    handler: projectQuery,
  },
  {
    name: 'project_management',
    description: '项目管理工具 - 执行项目级别的管理操作，如创建、配置、优化、导出等',
    inputSchema: {
      type: 'object',
      properties: {
        projectPath: {
          type: 'string',
          description: 'Godot项目的路径'
        },
        description: {
          type: 'string',
          description: '管理操作描述，用自然语言描述想要执行的管理操作'
        },
        action: {
          type: 'string',
          description: '管理操作类型（可选，用于向后兼容）',
          enum: ['create_project', 'configure_project', 'optimize_project', 'export_project', 'backup_project']
        },
        // 管理参数
        configuration: { type: 'object', description: '配置参数' },
        exportSettings: { type: 'object', description: '导出设置' },
        optimizationLevel: { type: 'string', description: '优化级别' },
        backupPath: { type: 'string', description: '备份路径' }
      },
      required: ['projectPath']
    },
    handler: projectManagement,
  }
];

/**
 * 传统工具到简化工具的映射
 * 用于向后兼容性
 */
export const toolMapping: Record<string, string> = {
  // 场景工具 -> godot_operation
  'create_scene': 'godot_operation',
  'add_node': 'godot_operation',
  'load_sprite': 'godot_operation',
  'save_scene': 'godot_operation',
  
  // 编辑器工具 -> godot_operation
  'launch_editor': 'godot_operation',
  'run_project': 'godot_operation',
  'get_debug_output': 'godot_operation',
  'stop_project': 'godot_operation',
  'get_godot_version': 'godot_operation',
  
  // 资源工具 -> godot_operation
  'export_mesh_library': 'godot_operation',
  'get_uid': 'godot_operation',
  'update_project_uids': 'godot_operation',
  
  // 项目工具 -> project_query
  'list_projects': 'project_query',
  'get_project_info': 'project_query'
};

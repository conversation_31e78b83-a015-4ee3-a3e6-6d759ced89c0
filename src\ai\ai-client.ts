/**
 * AI模型客户端
 * 封装OpenAI API调用，支持多种模型供应商
 */

import OpenAI from 'openai';
import { AIConfigManager, AIModelConfig } from './ai-config.js';
import { Logger } from '../utils/logger.js';
import * as fs from 'fs';
import * as path from 'path';

const logger = Logger.createScopedLogger('AIClient');

export interface AIMessage {
  role: 'system' | 'user' | 'assistant' | 'function';
  content: string;
  name?: string;
  function_call?: any;
}

export interface AIFunction {
  name: string;
  description: string;
  parameters: {
    type: string;
    properties: Record<string, any>;
    required: string[];
  };
}

export interface AICompletionOptions {
  messages: AIMessage[];
  functions?: AIFunction[];
  function_call?: 'auto' | 'none' | { name: string };
  temperature?: number;
  max_tokens?: number;
  stream?: boolean;
}

export interface AICompletionResult {
  content: string;
  function_call?: {
    name: string;
    arguments: string;
  };
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
  finish_reason: string;
}

/**
 * AI模型客户端
 */
export class AIClient {
  private openai!: OpenAI;
  private config: AIModelConfig;
  private configManager: AIConfigManager;

  constructor() {
    this.configManager = AIConfigManager.getInstance();
    this.config = this.configManager.getModelConfig();
    this.initializeClient();
  }

  /**
   * 初始化OpenAI客户端
   */
  private initializeClient(): void {
    try {
      this.openai = new OpenAI({
        apiKey: this.config.apiKey || 'not-needed',
        baseURL: this.config.baseURL,
        timeout: this.config.timeoutMs,
        maxRetries: this.config.maxRetries
      });

      logger.info('AI客户端初始化成功');
      logger.debug(`使用模型: ${this.config.model}`);
      logger.debug(`基础URL: ${this.config.baseURL}`);

    } catch (error) {
      logger.error('AI客户端初始化失败:', error);
      throw error;
    }
  }

  /**
   * 完成文本生成
   */
  public async complete(options: AICompletionOptions): Promise<AICompletionResult> {
    try {
      logger.debug('发送AI请求...');
      logger.debug(`消息数量: ${options.messages.length}`);
      
      const requestOptions: OpenAI.Chat.ChatCompletionCreateParams = {
        model: this.config.model,
        messages: options.messages as OpenAI.Chat.ChatCompletionMessageParam[],
        temperature: options.temperature ?? this.config.temperature,
        max_tokens: options.max_tokens ?? this.config.maxTokens,
        stream: false // 暂时禁用流式响应以简化类型处理
      };

      // 添加函数调用支持
      if (this.config.enableFunctionCalling && options.functions) {
        requestOptions.functions = options.functions as OpenAI.Chat.ChatCompletionCreateParams.Function[];
        if (options.function_call) {
          requestOptions.function_call = options.function_call;
        }
      }

      const response = await this.openai.chat.completions.create(requestOptions);
      
      const choice = response.choices[0];
      if (!choice) {
        throw new Error('AI响应中没有选择项');
      }

      const result: AICompletionResult = {
        content: choice.message.content || '',
        finish_reason: choice.finish_reason || 'unknown',
        usage: response.usage ? {
          prompt_tokens: response.usage.prompt_tokens,
          completion_tokens: response.usage.completion_tokens,
          total_tokens: response.usage.total_tokens
        } : undefined
      };

      // 处理函数调用
      if (choice.message.function_call) {
        result.function_call = {
          name: choice.message.function_call.name,
          arguments: choice.message.function_call.arguments
        };
      }

      logger.debug('AI请求完成');
      if (result.usage) {
        logger.debug(`Token使用: ${result.usage.total_tokens} (输入: ${result.usage.prompt_tokens}, 输出: ${result.usage.completion_tokens})`);
      }

      return result;

    } catch (error) {
      logger.error('AI请求失败:', error);
      throw new Error(`AI请求失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 简单的文本完成
   */
  public async simpleComplete(prompt: string, systemPrompt?: string): Promise<string> {
    const messages: AIMessage[] = [];
    
    if (systemPrompt) {
      messages.push({ role: 'system', content: systemPrompt });
    }
    
    messages.push({ role: 'user', content: prompt });

    const result = await this.complete({ messages });
    return result.content;
  }

  /**
   * 带函数调用的完成
   */
  public async completeWithFunctions(
    messages: AIMessage[], 
    functions: AIFunction[]
  ): Promise<AICompletionResult> {
    return this.complete({
      messages,
      functions,
      function_call: 'auto'
    });
  }

  /**
   * 检查AI服务是否可用
   */
  public async checkHealth(): Promise<boolean> {
    try {
      const result = await this.simpleComplete('Hello', 'Respond with "OK"');
      return result.toLowerCase().includes('ok');
    } catch (error) {
      logger.error('AI健康检查失败:', error);
      return false;
    }
  }

  /**
   * 获取模型信息
   */
  public getModelInfo(): Record<string, any> {
    return {
      model: this.config.model,
      baseURL: this.config.baseURL,
      maxTokens: this.config.maxTokens,
      temperature: this.config.temperature,
      enableFunctionCalling: this.config.enableFunctionCalling,
      enableStreaming: this.config.enableStreaming
    };
  }

  /**
   * 重新加载配置
   */
  public reloadConfig(): void {
    this.configManager.reloadConfig();
    this.config = this.configManager.getModelConfig();
    this.initializeClient();
    logger.info('AI客户端配置已重新加载');
  }

  /**
   * 估算token数量（简单估算）
   */
  public estimateTokens(text: string): number {
    // 简单估算：英文约4字符=1token，中文约1.5字符=1token
    const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
    const otherChars = text.length - chineseChars;
    return Math.ceil(chineseChars / 1.5 + otherChars / 4);
  }

  /**
   * 检查消息是否超过token限制
   */
  public checkTokenLimit(messages: AIMessage[]): { withinLimit: boolean; estimatedTokens: number } {
    const totalText = messages.map(m => m.content).join(' ');
    const estimatedTokens = this.estimateTokens(totalText);
    const withinLimit = estimatedTokens < this.config.maxTokens * 0.8; // 留20%余量

    return { withinLimit, estimatedTokens };
  }

  /**
   * 截断消息以适应token限制
   */
  public truncateMessages(messages: AIMessage[], maxTokens?: number): AIMessage[] {
    const limit = maxTokens || this.config.maxTokens * 0.8;
    const result: AIMessage[] = [];
    let currentTokens = 0;

    // 保留系统消息
    const systemMessages = messages.filter(m => m.role === 'system');
    for (const msg of systemMessages) {
      const tokens = this.estimateTokens(msg.content);
      if (currentTokens + tokens < limit) {
        result.push(msg);
        currentTokens += tokens;
      }
    }

    // 从最新消息开始添加
    const nonSystemMessages = messages.filter(m => m.role !== 'system').reverse();
    for (const msg of nonSystemMessages) {
      const tokens = this.estimateTokens(msg.content);
      if (currentTokens + tokens < limit) {
        result.unshift(msg);
        currentTokens += tokens;
      } else {
        break;
      }
    }

    return result;
  }
}

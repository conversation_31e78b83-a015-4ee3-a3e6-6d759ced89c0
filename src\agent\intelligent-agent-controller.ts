/**
 * 智能代理控制器
 * 在MCP服务中集成持久化Godot代理，提供高级智能操作接口
 */

import { EventEmitter } from 'events';
import { PersistentGodotManager, GodotCommand, GodotResponse } from './persistent-godot-manager.js';
import { GodotPathDetector } from '../godot/path-detector.js';
import { Logger } from '../utils/logger.js';
import { OperationParams, OperationResult } from '../core/types.js';
import { TaskPlanner, TaskPlan, PlanningContext } from '../ai/task-planner.js';
import { AIConfigManager } from '../ai/ai-config.js';

const logger = Logger.createScopedLogger('IntelligentAgentController');

export interface AgentTask {
  id: string;
  type: 'scene_operation' | 'project_query' | 'project_management' | 'debug_run';
  description: string;
  parameters: OperationParams;
  priority: number;
  timestamp: number;
}

export interface AgentContext {
  currentProject?: string;
  loadedScenes: string[];
  recentOperations: string[];
  agentState: 'idle' | 'busy' | 'error' | 'stopped';
}

/**
 * 智能代理控制器 - 管理持久化Godot代理并提供高级接口
 */
export class IntelligentAgentController extends EventEmitter {
  private persistentManager: PersistentGodotManager;
  private pathDetector: GodotPathDetector;
  private context: AgentContext;
  private taskQueue: AgentTask[] = [];
  private isProcessingTasks: boolean = false;
  private currentTask: AgentTask | null = null;
  private taskPlanner: TaskPlanner;
  private aiConfigManager: AIConfigManager;

  constructor(pathDetector: GodotPathDetector) {
    super();
    this.pathDetector = pathDetector;
    this.persistentManager = new PersistentGodotManager(pathDetector);
    this.taskPlanner = new TaskPlanner();
    this.aiConfigManager = AIConfigManager.getInstance();
    this.context = {
      loadedScenes: [],
      recentOperations: [],
      agentState: 'idle'
    };

    this.setupEventListeners();
  }

  /**
   * 启动智能代理系统
   */
  public async startAgent(projectPath: string): Promise<OperationResult> {
    try {
      logger.info('启动智能代理系统...');
      
      // 启动持久化Godot代理
      await this.persistentManager.startAgent(projectPath);
      
      // 更新上下文
      this.context.currentProject = projectPath;
      this.context.agentState = 'idle';
      
      // 发送初始化命令
      await this.sendCommand({
        action: 'load_project',
        project_path: projectPath
      });

      logger.info('智能代理系统启动成功');
      this.emit('agent_started', { projectPath });

      return {
        success: true,
        message: '智能代理系统启动成功',
        data: { projectPath, agentState: this.context.agentState }
      };

    } catch (error) {
      logger.error('启动智能代理系统失败:', error);
      this.context.agentState = 'error';
      
      return {
        success: false,
        error: `启动智能代理系统失败: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  }

  /**
   * 停止智能代理系统
   */
  public async stopAgent(): Promise<OperationResult> {
    try {
      logger.info('停止智能代理系统...');
      
      // 停止任务处理
      this.isProcessingTasks = false;
      this.currentTask = null;
      this.taskQueue = [];
      
      // 停止持久化代理
      await this.persistentManager.stopAgent();
      
      // 更新上下文
      this.context.agentState = 'stopped';
      this.context.currentProject = undefined;
      this.context.loadedScenes = [];
      
      logger.info('智能代理系统已停止');
      this.emit('agent_stopped');

      return {
        success: true,
        message: '智能代理系统已停止'
      };

    } catch (error) {
      logger.error('停止智能代理系统失败:', error);
      
      return {
        success: false,
        error: `停止智能代理系统失败: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  }

  /**
   * 执行智能场景操作
   */
  public async executeSceneOperation(params: OperationParams): Promise<OperationResult> {
    const task: AgentTask = {
      id: this.generateTaskId(),
      type: 'scene_operation',
      description: `场景操作: ${params.operation || 'unknown'}`,
      parameters: params,
      priority: 1,
      timestamp: Date.now()
    };

    return this.queueTask(task);
  }

  /**
   * 查询项目状态
   */
  public async queryProjectState(params: OperationParams): Promise<OperationResult> {
    const task: AgentTask = {
      id: this.generateTaskId(),
      type: 'project_query',
      description: `项目查询: ${params.query || 'general'}`,
      parameters: params,
      priority: 2,
      timestamp: Date.now()
    };

    return this.queueTask(task);
  }

  /**
   * 管理项目
   */
  public async manageProject(params: OperationParams): Promise<OperationResult> {
    const task: AgentTask = {
      id: this.generateTaskId(),
      type: 'project_management',
      description: `项目管理: ${params.action || 'unknown'}`,
      parameters: params,
      priority: 3,
      timestamp: Date.now()
    };

    return this.queueTask(task);
  }

  /**
   * 调试和运行
   */
  public async debugAndRun(params: OperationParams): Promise<OperationResult> {
    const task: AgentTask = {
      id: this.generateTaskId(),
      type: 'debug_run',
      description: `调试运行: ${params.scene || 'main'}`,
      parameters: params,
      priority: 0, // 最高优先级
      timestamp: Date.now()
    };

    return this.queueTask(task);
  }

  /**
   * 执行AI增强的智能操作
   */
  public async executeIntelligentOperation(params: {
    projectPath: string;
    description: string;
    context?: Record<string, any>;
  }): Promise<OperationResult> {
    try {
      logger.info(`执行AI智能操作: ${params.description}`);

      // 检查AI是否可用
      if (!this.aiConfigManager.isAIEnabled()) {
        logger.warn('AI功能未启用，使用传统操作模式');
        return {
          success: false,
          error: 'AI功能未启用',
          message: '请配置AI模型以使用智能操作功能'
        };
      }

      // 构建规划上下文
      const planningContext: PlanningContext = {
        projectPath: params.projectPath,
        currentScenes: this.context.loadedScenes,
        recentOperations: this.context.recentOperations,
        userPreferences: params.context
      };

      // 使用AI规划任务
      const taskPlan = await this.taskPlanner.planTask(params.description, planningContext);

      logger.info(`AI生成任务计划: ${taskPlan.steps.length}个步骤`);

      // 执行任务计划
      return await this.executeTaskPlan(taskPlan, params.projectPath);

    } catch (error) {
      logger.error('AI智能操作失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        message: 'AI智能操作执行失败'
      };
    }
  }

  /**
   * 执行任务计划
   */
  private async executeTaskPlan(taskPlan: TaskPlan, projectPath: string): Promise<OperationResult> {
    try {
      logger.info(`开始执行任务计划: ${taskPlan.description}`);

      const results: OperationResult[] = [];
      let overallSuccess = true;

      // 按步骤执行
      for (const step of taskPlan.steps) {
        logger.debug(`执行步骤: ${step.action} - ${step.reason}`);

        const task: AgentTask = {
          id: this.generateTaskId(),
          type: taskPlan.task_type,
          description: step.reason,
          parameters: { ...step.parameters, projectPath },
          priority: step.priority,
          timestamp: Date.now()
        };

        const result = await this.executeTask(task);
        results.push(result);

        if (!result.success) {
          overallSuccess = false;
          logger.warn(`步骤执行失败: ${step.action} - ${result.error}`);

          // 根据配置决定是否继续执行
          if (step.priority <= 2) { // 高优先级步骤失败则停止
            break;
          }
        }
      }

      // 更新操作历史
      this.context.recentOperations.push(taskPlan.description);
      if (this.context.recentOperations.length > 10) {
        this.context.recentOperations = this.context.recentOperations.slice(-10);
      }

      return {
        success: overallSuccess,
        data: {
          taskPlan,
          stepResults: results,
          executedSteps: results.length,
          totalSteps: taskPlan.steps.length
        },
        message: overallSuccess
          ? `任务计划执行成功，完成 ${results.length}/${taskPlan.steps.length} 个步骤`
          : `任务计划部分失败，完成 ${results.filter(r => r.success).length}/${taskPlan.steps.length} 个步骤`
      };

    } catch (error) {
      logger.error('任务计划执行失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        message: '任务计划执行过程中发生错误'
      };
    }
  }

  /**
   * 获取代理状态
   */
  public getAgentStatus(): AgentContext & { isRunning: boolean; queueLength: number } {
    return {
      ...this.context,
      isRunning: this.persistentManager.isAgentRunning(),
      queueLength: this.taskQueue.length
    };
  }

  /**
   * 将任务加入队列
   */
  private async queueTask(task: AgentTask): Promise<OperationResult> {
    logger.debug(`任务入队: ${task.description}`);
    
    // 按优先级插入任务
    const insertIndex = this.taskQueue.findIndex(t => t.priority > task.priority);
    if (insertIndex === -1) {
      this.taskQueue.push(task);
    } else {
      this.taskQueue.splice(insertIndex, 0, task);
    }

    // 开始处理任务队列
    if (!this.isProcessingTasks) {
      this.processTaskQueue();
    }

    // 等待任务完成
    return new Promise((resolve) => {
      const onTaskComplete = (completedTask: AgentTask, result: OperationResult) => {
        if (completedTask.id === task.id) {
          this.off('task_completed', onTaskComplete);
          resolve(result);
        }
      };
      this.on('task_completed', onTaskComplete);
    });
  }

  /**
   * 处理任务队列
   */
  private async processTaskQueue(): Promise<void> {
    if (this.isProcessingTasks || this.taskQueue.length === 0) {
      return;
    }

    this.isProcessingTasks = true;
    this.context.agentState = 'busy';

    while (this.taskQueue.length > 0 && this.isProcessingTasks) {
      const task = this.taskQueue.shift()!;
      this.currentTask = task;

      logger.info(`处理任务: ${task.description}`);
      
      try {
        const result = await this.executeTask(task);
        this.emit('task_completed', task, result);
        
        // 更新上下文
        this.context.recentOperations.push(task.description);
        if (this.context.recentOperations.length > 10) {
          this.context.recentOperations.shift();
        }

      } catch (error) {
        logger.error(`任务执行失败: ${task.description}`, error);
        const errorResult: OperationResult = {
          success: false,
          error: `任务执行失败: ${error instanceof Error ? error.message : String(error)}`
        };
        this.emit('task_completed', task, errorResult);
      }
    }

    this.isProcessingTasks = false;
    this.currentTask = null;
    this.context.agentState = 'idle';
  }

  /**
   * 执行具体任务
   */
  private async executeTask(task: AgentTask): Promise<OperationResult> {
    switch (task.type) {
      case 'scene_operation':
        return this.executeSceneOperationTask(task);
      case 'project_query':
        return this.executeProjectQueryTask(task);
      case 'project_management':
        return this.executeProjectManagementTask(task);
      case 'debug_run':
        return this.executeDebugRunTask(task);
      default:
        throw new Error(`未知任务类型: ${task.type}`);
    }
  }

  /**
   * 执行场景操作任务
   */
  private async executeSceneOperationTask(task: AgentTask): Promise<OperationResult> {
    const { scenePath, operation, nodeData } = task.parameters;
    
    if (operation === 'create_scene') {
      const response = await this.sendCommand({
        action: 'create_scene',
        scene_path: scenePath,
        root_type: nodeData?.rootType || 'Node2D'
      });
      
      if (response.type === 'scene_created') {
        this.context.loadedScenes.push(scenePath);
        return {
          success: true,
          message: `场景创建成功: ${scenePath}`,
          data: response
        };
      }
    }
    
    // 其他场景操作...
    return {
      success: false,
      error: '场景操作未实现'
    };
  }

  /**
   * 执行项目查询任务
   */
  private async executeProjectQueryTask(task: AgentTask): Promise<OperationResult> {
    // 实现项目查询逻辑
    return {
      success: true,
      message: '项目查询完成',
      data: this.context
    };
  }

  /**
   * 执行项目管理任务
   */
  private async executeProjectManagementTask(task: AgentTask): Promise<OperationResult> {
    // 实现项目管理逻辑
    return {
      success: true,
      message: '项目管理操作完成'
    };
  }

  /**
   * 执行调试运行任务
   */
  private async executeDebugRunTask(task: AgentTask): Promise<OperationResult> {
    // 实现调试运行逻辑
    return {
      success: true,
      message: '调试运行完成'
    };
  }

  /**
   * 发送命令到持久化代理
   */
  private async sendCommand(command: GodotCommand): Promise<GodotResponse> {
    return this.persistentManager.sendCommand(command);
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    this.persistentManager.on('response', (response: GodotResponse) => {
      logger.debug('收到代理响应:', response);
      this.emit('agent_response', response);
    });

    this.persistentManager.on('stopped', () => {
      logger.info('持久化代理已停止');
      this.context.agentState = 'stopped';
      this.emit('agent_stopped');
    });
  }

  /**
   * 生成任务ID
   */
  private generateTaskId(): string {
    return `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

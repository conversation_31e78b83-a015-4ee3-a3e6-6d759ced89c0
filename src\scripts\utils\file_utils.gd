#!/usr/bin/env -S godot --headless --script
# GDScript File Utilities Module
# Provides file system utilities for Godot operations

class_name GodotFileUtils

# Find files with specific extension in a directory
static func find_files(path: String, extension: String) -> Array:
	var files = []
	var dir = DirAccess.open(path)
	
	if not dir:
		GodotLogger.log_error("Cannot open directory: " + path)
		return files
	
	GodotLogger.log_debug("Searching for ." + extension + " files in: " + path)
	
	dir.list_dir_begin()
	var file_name = dir.get_next()
	
	while file_name != "":
		var full_path = path + "/" + file_name
		
		if dir.current_is_dir():
			# Recursively search subdirectories
			var subdir_files = find_files(full_path, extension)
			files.append_array(subdir_files)
		else:
			# Check if file has the desired extension
			if file_name.ends_with("." + extension):
				files.append(full_path)
				GodotLogger.log_debug("Found file: " + full_path)
		
		file_name = dir.get_next()
	
	dir.list_dir_end()
	
	GodotLogger.log_debug("Found " + str(files.size()) + " ." + extension + " files")
	return files

# Find files with multiple extensions
static func find_files_with_extensions(path: String, extensions: Array) -> Array:
	var all_files = []
	
	for extension in extensions:
		var files = find_files(path, extension)
		all_files.append_array(files)
	
	return all_files

# Check if directory exists
static func directory_exists(path: String) -> bool:
	var dir = DirAccess.open(path)
	return dir != null

# Create directory if it doesn't exist
static func ensure_directory_exists(path: String) -> bool:
	if directory_exists(path):
		return true
	
	var dir = DirAccess.open("res://")
	if not dir:
		GodotLogger.log_error("Cannot access res:// directory")
		return false
	
	var error = dir.make_dir_recursive(path)
	if error == OK:
		GodotLogger.log_debug("Created directory: " + path)
		return true
	else:
		GodotLogger.log_error("Failed to create directory: " + path + " (Error: " + str(error) + ")")
		return false

# Get file extension
static func get_file_extension(file_path: String) -> String:
	var parts = file_path.split(".")
	if parts.size() > 1:
		return parts[-1]
	return ""

# Get file name without extension
static func get_file_name_without_extension(file_path: String) -> String:
	var file_name = file_path.get_file()
	var extension = get_file_extension(file_name)
	if extension.is_empty():
		return file_name
	return file_name.substr(0, file_name.length() - extension.length() - 1)

# Get directory path from file path
static func get_directory_path(file_path: String) -> String:
	return file_path.get_base_dir()

# Normalize path separators
static func normalize_path(path: String) -> String:
	return path.replace("\\", "/")

# Convert absolute path to resource path
static func absolute_to_resource_path(absolute_path: String) -> String:
	var project_path = ProjectSettings.globalize_path("res://")
	if absolute_path.begins_with(project_path):
		var relative_path = absolute_path.substr(project_path.length())
		return "res://" + relative_path.lstrip("/")
	return absolute_path

# Convert resource path to absolute path
static func resource_to_absolute_path(resource_path: String) -> String:
	return ProjectSettings.globalize_path(resource_path)

# Check if path is a resource path
static func is_resource_path(path: String) -> bool:
	return path.begins_with("res://") or path.begins_with("user://")

# Validate file path format
static func validate_file_path(path: String, required_extension: String = "") -> bool:
	if path.is_empty():
		GodotLogger.log_error("File path cannot be empty")
		return false
	
	if not required_extension.is_empty():
		if not path.ends_with("." + required_extension):
			GodotLogger.log_error("File must have ." + required_extension + " extension")
			return false
	
	return true

# Godot Operations Helper Functions
# This file contains the remaining helper functions for resource operations

# Process mesh nodes and add them to MeshLibrary
static func process_mesh_nodes(scene_root: Node, mesh_library: MeshLibrary, mesh_item_names: Array) -> int:
	var use_specific_items = mesh_item_names.size() > 0
	var item_id = 0
	
	if use_specific_items:
		print("[DEBUG] Using specific mesh items: " + str(mesh_item_names))
	else:
		print("[DEBUG] Using all mesh items in the scene")
	
	print("[DEBUG] Processing child nodes...")
	
	for child in scene_root.get_children():
		print("[DEBUG] Checking child node: " + child.name)
		
		# Skip if not using all items and this item is not in the list
		if use_specific_items and not (child.name in mesh_item_names):
			print("[DEBUG] Skipping node " + child.name + " (not in specified items list)")
			continue
		
		# Find MeshInstance3D
		var mesh_instance = find_mesh_instance(child)
		
		if mesh_instance and mesh_instance.mesh:
			print("[DEBUG] Adding mesh: " + child.name)
			
			# Add mesh to library
			mesh_library.create_item(item_id)
			mesh_library.set_item_name(item_id, child.name)
			mesh_library.set_item_mesh(item_id, mesh_instance.mesh)
			
			# Add collision shape if available
			add_collision_shape(mesh_library, item_id, child)
			
			# Add preview
			mesh_library.set_item_preview(item_id, mesh_instance.mesh)
			print("[DEBUG] Added mesh to library with ID: " + str(item_id))
			
			item_id += 1
		else:
			print("[DEBUG] Node " + child.name + " has no valid mesh")
	
	print("[DEBUG] Processed " + str(item_id) + " meshes")
	return item_id

# Find MeshInstance3D in node or its descendants
static func find_mesh_instance(node: Node) -> MeshInstance3D:
	if node is MeshInstance3D:
		print("[DEBUG] Node " + node.name + " is a MeshInstance3D")
		return node
	
	# Search descendants
	print("[DEBUG] Searching for MeshInstance3D in descendants of " + node.name)
	for child in node.get_children():
		if child is MeshInstance3D:
			print("[DEBUG] Found MeshInstance3D in descendant: " + child.name)
			return child
	
	return null

# Add collision shape to mesh library item
static func add_collision_shape(mesh_library: MeshLibrary, item_id: int, node: Node):
	var collision_added = false
	
	for child in node.get_children():
		if child is CollisionShape3D and child.shape:
			mesh_library.set_item_shapes(item_id, [child.shape])
			print("[DEBUG] Added collision shape from: " + child.name)
			collision_added = true
			break
	
	if not collision_added:
		print("[DEBUG] No collision shape found for mesh: " + node.name)

# Find files recursively with specific extension
static func find_files(path: String, extension: String) -> Array:
	var files = []
	var dir = DirAccess.open(path)
	
	if not dir:
		print("[ERROR] Failed to open directory: " + path)
		return files
	
	print("[DEBUG] Searching for ." + extension + " files in: " + path)
	
	dir.list_dir_begin()
	var file_name = dir.get_next()
	
	while file_name != "":
		var full_path = path + "/" + file_name
		
		if dir.current_is_dir() and not file_name.begins_with("."):
			# Recursively search subdirectories
			var subdir_files = find_files(full_path, extension)
			files.append_array(subdir_files)
		elif file_name.ends_with("." + extension):
			files.append(full_path)
			print("[DEBUG] Found file: " + full_path)
		
		file_name = dir.get_next()
	
	dir.list_dir_end()
	return files

# Resave all scene files
static func resave_scenes(project_path: String) -> Dictionary:
	print("[DEBUG] Searching for scene files in: " + project_path)
	var scenes = find_files(project_path, "tscn")
	print("[DEBUG] Found " + str(scenes.size()) + " scenes")
	
	var success_count = 0
	var error_count = 0
	
	for scene_path in scenes:
		print("[DEBUG] Processing scene: " + scene_path)
		
		if not FileAccess.file_exists(scene_path):
			print("[ERROR] Scene file does not exist: " + scene_path)
			error_count += 1
			continue
		
		var scene = load(scene_path)
		if scene:
			var save_error = ResourceSaver.save(scene, scene_path)
			if save_error == OK:
				success_count += 1
				print("[DEBUG] Scene saved successfully: " + scene_path)
			else:
				error_count += 1
				print("[ERROR] Failed to save scene: " + scene_path + ", error: " + str(save_error))
		else:
			error_count += 1
			print("[ERROR] Failed to load scene: " + scene_path)
	
	return {
		"total": scenes.size(),
		"success": success_count,
		"errors": error_count
	}

# Resave scripts and shaders to generate UIDs
static func resave_scripts_and_shaders(project_path: String) -> Dictionary:
	print("[DEBUG] Searching for script and shader files in: " + project_path)
	
	var scripts = []
	scripts.append_array(find_files(project_path, "gd"))
	scripts.append_array(find_files(project_path, "shader"))
	scripts.append_array(find_files(project_path, "gdshader"))
	
	print("[DEBUG] Found " + str(scripts.size()) + " scripts/shaders")
	
	var missing_uids = 0
	var generated_uids = 0
	
	for script_path in scripts:
		print("[DEBUG] Checking UID for: " + script_path)
		var uid_path = script_path + ".uid"
		
		if not FileAccess.file_exists(uid_path):
			missing_uids += 1
			print("[DEBUG] Missing UID file for: " + script_path + ", generating...")
			
			# Force save to generate UID
			var resource = load(script_path)
			if resource:
				var save_error = ResourceSaver.save(resource, script_path)
				if save_error == OK:
					generated_uids += 1
					print("[DEBUG] Generated UID for: " + script_path)
				else:
					print("[ERROR] Failed to generate UID for: " + script_path + ", error: " + str(save_error))
			else:
				print("[ERROR] Failed to load resource: " + script_path)
		else:
			print("[DEBUG] UID file already exists for: " + script_path)
	
	return {
		"missing": missing_uids,
		"generated": generated_uids
	}

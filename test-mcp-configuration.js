#!/usr/bin/env node
/**
 * 测试MCP配置和智能代理功能
 */

import { GodotMCPServer } from './build/core/server.js';
import { AIConfigManager } from './build/ai/ai-config.js';

async function testMCPConfiguration() {
  try {
    console.log('🔧 测试MCP配置和智能代理功能...\n');
    
    // 1. 测试环境变量配置
    console.log('1. 环境变量配置检查:');
    console.log(`   USE_SIMPLIFIED_INTERFACE: ${process.env.USE_SIMPLIFIED_INTERFACE || 'not set'}`);
    console.log(`   GODOT_PATH: ${process.env.GODOT_PATH || 'not set'}`);
    console.log(`   DEBUG: ${process.env.DEBUG || 'not set'}`);
    
    // 2. 测试AI配置
    console.log('\n2. AI配置检查:');
    const aiConfig = AIConfigManager.getInstance();
    const configSummary = aiConfig.getConfigSummary();
    
    console.log(`   AI功能状态: ${aiConfig.isAIEnabled() ? '✅ 已启用' : '❌ 未启用'}`);
    console.log(`   模型供应商: ${configSummary.model.provider}`);
    console.log(`   模型名称: ${configSummary.model.model}`);
    console.log(`   API密钥: ${configSummary.model.apiKey ? '✅ 已配置' : '❌ 未配置'}`);
    
    // 3. 测试简化接口服务器
    console.log('\n3. 启动简化接口服务器测试...');
    process.env.USE_SIMPLIFIED_INTERFACE = 'true';
    
    const simplifiedServer = new GodotMCPServer();
    await simplifiedServer.start();
    
    const stats = simplifiedServer.getServerStats();
    console.log(`   ✅ 服务器启动成功`);
    console.log(`   📊 工具总数: ${stats.toolRegistry.totalTools}`);
    console.log(`   🔧 接口类型: ${stats.toolRegistry.totalTools === 3 ? '简化接口' : '传统接口'}`);
    
    // 4. 列出可用工具
    console.log('\n4. 可用工具列表:');
    const tools = stats.toolRegistry.tools;
    tools.forEach((tool, index) => {
      console.log(`   ${index + 1}. ${tool.name}: ${tool.description}`);
    });
    
    await simplifiedServer.stop();
    
    // 5. 测试传统接口服务器
    console.log('\n5. 启动传统接口服务器测试...');
    process.env.USE_SIMPLIFIED_INTERFACE = 'false';
    
    const traditionalServer = new GodotMCPServer();
    await traditionalServer.start();
    
    const traditionalStats = traditionalServer.getServerStats();
    console.log(`   ✅ 服务器启动成功`);
    console.log(`   📊 工具总数: ${traditionalStats.toolRegistry.totalTools}`);
    console.log(`   🔧 接口类型: ${traditionalStats.toolRegistry.totalTools === 18 ? '传统接口' : '简化接口'}`);
    
    await traditionalServer.stop();
    
    // 6. 配置建议
    console.log('\n6. MCP配置建议:');
    
    console.log('\n📋 推荐的MCP客户端配置 (简化接口):');
    console.log('```json');
    console.log('{');
    console.log('  "mcpServers": {');
    console.log('    "godot-mcp": {');
    console.log('      "command": "node",');
    console.log('      "args": ["D:/MCP/test-mcp/godot-mcp-1/build/index.js"],');
    console.log('      "env": {');
    console.log('        "USE_SIMPLIFIED_INTERFACE": "true",');
    console.log('        "GODOT_PATH": "D:\\\\Godot_v4.2.1-stable_mono_win64\\\\godot.exe",');
    console.log('        "DEBUG": "false"');
    console.log('      }');
    console.log('    }');
    console.log('  }');
    console.log('}');
    console.log('```');
    
    console.log('\n📋 传统接口配置:');
    console.log('```json');
    console.log('{');
    console.log('  "mcpServers": {');
    console.log('    "godot-mcp-traditional": {');
    console.log('      "command": "node",');
    console.log('      "args": ["D:/MCP/test-mcp/godot-mcp-1/build/index.js"],');
    console.log('      "env": {');
    console.log('        "USE_SIMPLIFIED_INTERFACE": "false",');
    console.log('        "GODOT_PATH": "D:\\\\Godot_v4.2.1-stable_mono_win64\\\\godot.exe",');
    console.log('        "DEBUG": "false"');
    console.log('      }');
    console.log('    }');
    console.log('  }');
    console.log('}');
    console.log('```');
    
    // 7. 故障排除建议
    console.log('\n7. 智能代理启动故障排除:');
    console.log('   如果遇到 "Timeout waiting for Godot agent to start" 错误:');
    console.log('   ✅ 检查GODOT_PATH环境变量是否正确设置');
    console.log('   ✅ 确保项目路径存在且包含project.godot文件');
    console.log('   ✅ 检查Godot可执行文件是否可以正常运行');
    console.log('   ✅ 确保没有防火墙或权限问题');
    console.log('   ✅ 查看详细日志输出以获取更多信息');
    console.log('   ✅ 超时时间已增加到30秒，请耐心等待');
    
    console.log('\n🎉 MCP配置测试完成!');
    
    if (!aiConfig.isAIEnabled()) {
      console.log('\n⚠️  注意: 要使用简化接口的AI功能，请在.env文件中配置AI模型参数');
    } else {
      console.log('\n✅ 系统已就绪，可以使用简化接口的AI增强功能');
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    console.error('错误详情:', error.stack);
  }
}

// 运行测试
testMCPConfiguration().catch(console.error);

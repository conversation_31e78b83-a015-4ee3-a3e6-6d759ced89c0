#!/usr/bin/env -S godot --headless --script
# Refactored Godot Operations
# Modular version with 100% backward compatibility
# All modules are contained in this single file to avoid dependency issues

extends SceneTree

# Global variables
var debug_mode: bool = false

# Main entry point
func _init():
	# Parse command line arguments
	var args = OS.get_cmdline_args()
	debug_mode = "--debug-godot" in args
	
	if debug_mode:
		log_debug_args()
		log_debug_environment()
	
	# Find the script argument and determine positions
	var script_index = args.find("--script")
	if script_index == -1:
		log_error("Could not find --script argument")
		quit(1)
		return
	
	# The operation should be 2 positions after the script path
	var operation_index = script_index + 2
	# The params should be 3 positions after the script path
	var params_index = script_index + 3
	
	if args.size() <= params_index:
		log_error("Usage: godot --headless --script godot_operations.gd <operation> <json_params>")
		log_error("Not enough command-line arguments provided.")
		quit(1)
		return
	
	var operation = args[operation_index]
	var params_json = args[params_index]
	
	log_info("Operation: " + operation)
	log_debug("Params JSON: " + params_json)
	
	# Parse JSON parameters
	var params = parse_json_params(params_json)
	if params == null:
		quit(1)
		return
	
	log_info("Executing operation: " + operation)
	
	# Route operation to appropriate handler
	var success = execute_operation(operation, params)
	
	if success:
		log_info("Operation completed successfully")
		quit(0)
	else:
		log_error("Operation failed")
		quit(1)

# Execute operation
func execute_operation(operation: String, params: Dictionary) -> bool:
	match operation:
		"create_scene":
			return create_scene(params)
		"add_node":
			return add_node(params)
		"load_sprite":
			return load_sprite(params)
		"save_scene":
			return save_scene(params)
		"export_mesh_library":
			return export_mesh_library(params)
		"get_uid":
			return get_uid(params)
		"resave_resources":
			return resave_resources(params)
		_:
			log_error("Unknown operation: " + operation)
			return false

# Parse JSON parameters with error handling
func parse_json_params(params_json: String) -> Dictionary:
	var json = JSON.new()
	var error = json.parse(params_json)
	
	if error == OK:
		var params = json.get_data()
		if params is Dictionary:
			return params
		else:
			log_error("JSON parameters must be a dictionary/object")
			return {}
	else:
		log_error("Failed to parse JSON parameters: " + params_json)
		log_error("JSON Error: " + json.get_error_message() + " at line " + str(json.get_error_line()))
		return {}

# Validate required parameters for an operation
func validate_required_params(params: Dictionary, required_keys: Array) -> bool:
	for key in required_keys:
		if not params.has(key):
			log_error("Missing required parameter: " + key)
			return false
		if params[key] == null or (params[key] is String and params[key].is_empty()):
			log_error("Parameter '" + key + "' cannot be empty")
			return false
	return true

# Normalize file paths (ensure they start with res://)
func normalize_scene_path(path: String) -> String:
	if not path.begins_with("res://"):
		return "res://" + path
	return path

# Get absolute path from Godot resource path
func get_absolute_path(resource_path: String) -> String:
	return ProjectSettings.globalize_path(resource_path)

# Validate that a file exists
func validate_file_exists(path: String, file_type: String = "file") -> bool:
	var absolute_path = get_absolute_path(path)
	log_debug("Checking if " + file_type + " exists: " + absolute_path)
	
	if not FileAccess.file_exists(absolute_path):
		log_error(file_type.capitalize() + " does not exist at: " + absolute_path)
		return false
	
	return true

# Logging functions
func log_debug(message: String):
	if debug_mode:
		print("[DEBUG] " + message)

func log_info(message: String):
	print("[INFO] " + message)

func log_error(message: String):
	printerr("[ERROR] " + message)

func log_warn(message: String):
	print("[WARN] " + message)

func log_success(message: String):
	print("[SUCCESS] " + message)

# Debug environment information
func log_debug_environment():
	if not debug_mode:
		return
		
	log_debug("=== Environment Debug Info ===")
	
	# Project paths
	var project_res_path = "res://"
	var project_user_path = "user://"
	var global_res_path = ProjectSettings.globalize_path(project_res_path)
	var global_user_path = ProjectSettings.globalize_path(project_user_path)
	
	log_debug("Project paths:")
	log_debug("- res:// path: " + project_res_path)
	log_debug("- user:// path: " + project_user_path)
	log_debug("- Globalized res:// path: " + global_res_path)
	log_debug("- Globalized user:// path: " + global_user_path)
	
	# Environment variables
	log_debug("Environment variables:")
	var env_vars = ["PATH", "HOME", "USER", "TEMP", "GODOT_PATH"]
	for env_var in env_vars:
		if OS.has_environment(env_var):
			log_debug("  " + env_var + " = " + OS.get_environment(env_var))
	
	log_debug("=== End Environment Debug Info ===")

# Log command line arguments for debugging
func log_debug_args():
	if not debug_mode:
		return
		
	var args = OS.get_cmdline_args()
	log_debug("Command line arguments (" + str(args.size()) + "):")
	for i in range(args.size()):
		log_debug("  [" + str(i) + "] " + args[i])

# Get script by class name or path
func get_script_by_name(name_of_class: String) -> Script:
	log_debug("Attempting to get script for class: " + name_of_class)
	
	# Try to load it directly if it's a resource path
	if ResourceLoader.exists(name_of_class, "Script"):
		log_debug("Resource exists, loading directly: " + name_of_class)
		var script = load(name_of_class) as Script
		if script:
			log_debug("Successfully loaded script from path")
			return script
		else:
			log_error("Failed to load script from path: " + name_of_class)
	else:
		log_debug("Resource not found, checking global class registry")
	
	# Search for it in the global class registry if it's a class name
	var global_classes = ProjectSettings.get_global_class_list()
	log_debug("Searching through " + str(global_classes.size()) + " global classes")
	
	for global_class in global_classes:
		var found_name_of_class = global_class["class"]
		var found_path = global_class["path"]
		
		if found_name_of_class == name_of_class:
			log_debug("Found matching class in registry: " + found_name_of_class + " at path: " + found_path)
			var script = load(found_path) as Script
			if script:
				log_debug("Successfully loaded script from registry")
				return script
			else:
				log_error("Failed to load script from registry path: " + found_path)
				break
	
	log_error("Could not find script for class: " + name_of_class)
	return null

# Instantiate a class by name
func instantiate_class(name_of_class: String) -> Object:
	if name_of_class.is_empty():
		log_error("Cannot instantiate class: name is empty")
		return null
	
	var result = null
	
	log_debug("Attempting to instantiate class: " + name_of_class)
	
	# First, try to get it as a built-in class
	if ClassDB.class_exists(name_of_class):
		log_debug("Found built-in class: " + name_of_class)
		result = ClassDB.instantiate(name_of_class)
		if result:
			log_debug("Successfully instantiated built-in class")
			return result
		else:
			log_error("Failed to instantiate built-in class: " + name_of_class)
	else:
		log_debug("Not a built-in class, trying script-based class")
	
	# If not a built-in class, try to get the script and instantiate it
	var script = get_script_by_name(name_of_class)
	if script:
		log_debug("Found script for class, attempting to instantiate")
		result = script.new()
		if result:
			log_debug("Successfully instantiated script-based class")
			return result
		else:
			log_error("Failed to instantiate script-based class")
	
	log_error("Could not instantiate class: " + name_of_class)
	return null

# ===== SCENE OPERATIONS =====

# Create a new scene
func create_scene(params: Dictionary) -> bool:
	if not validate_required_params(params, ["scene_path"]):
		return false

	var scene_path = params.scene_path
	var root_node_type = params.get("root_node_type", "Node2D")

	log_info("Creating scene: " + scene_path)
	log_debug("Root node type: " + root_node_type)

	var full_scene_path = normalize_scene_path(scene_path)
	log_debug("Full scene path: " + full_scene_path)

	# Create root node
	var root_node = instantiate_class(root_node_type)
	if not root_node:
		return false

	log_debug("Root node created successfully")

	# Ensure scene directory exists
	if not ensure_scene_directory_exists(full_scene_path):
		return false

	# Create and save scene
	var scene = PackedScene.new()
	var pack_result = scene.pack(root_node)

	if pack_result != OK:
		log_error("Failed to pack scene: " + str(pack_result))
		return false

	log_debug("Scene packed successfully")

	var save_error = ResourceSaver.save(scene, full_scene_path)

	if save_error == OK:
		if verify_scene_created(full_scene_path):
			log_success("Scene created successfully: " + scene_path)
			return true
		else:
			log_error("Scene reported as saved but verification failed")
			return false
	else:
		log_error("Failed to save scene: " + str(save_error))
		return false

# Add a node to an existing scene
func add_node(params: Dictionary) -> bool:
	if not validate_required_params(params, ["scene_path", "node_type", "node_name"]):
		return false

	var scene_path = params.scene_path
	var node_type = params.node_type
	var node_name = params.node_name
	var parent_node_path = params.get("parent_node_path", "root")
	var properties = params.get("properties", {})

	log_info("Adding node to scene: " + scene_path)
	log_debug("Node type: " + node_type)
	log_debug("Node name: " + node_name)
	log_debug("Parent path: " + parent_node_path)

	var full_scene_path = normalize_scene_path(scene_path)

	if not validate_file_exists(full_scene_path, "scene"):
		return false

	# Load and instantiate the scene
	var scene = load(full_scene_path)
	if not scene:
		log_error("Failed to load scene: " + full_scene_path)
		return false

	var scene_root = scene.instantiate()
	if not scene_root:
		log_error("Failed to instantiate scene")
		return false

	log_debug("Scene loaded and instantiated successfully")

	# Create the new node
	var new_node = instantiate_class(node_type)
	if not new_node:
		return false

	new_node.name = node_name
	log_debug("New node created with name: " + node_name)

	# Apply properties if provided
	apply_node_properties(new_node, properties)

	# Find parent node and add the new node
	var parent_node = find_node_by_path(scene_root, parent_node_path)
	if not parent_node:
		log_error("Parent node not found: " + parent_node_path)
		return false

	parent_node.add_child(new_node)
	new_node.owner = scene_root
	log_debug("Node added to parent successfully")

	# Save the modified scene
	return save_scene_instance(scene_root, full_scene_path, scene_path)

# Load a sprite into a Sprite2D node
func load_sprite(params: Dictionary) -> bool:
	if not validate_required_params(params, ["scene_path", "node_path", "texture_path"]):
		return false

	var scene_path = params.scene_path
	var node_path = params.node_path
	var texture_path = params.texture_path

	log_info("Loading sprite into scene: " + scene_path)
	log_debug("Node path: " + node_path)
	log_debug("Texture path: " + texture_path)

	var full_scene_path = normalize_scene_path(scene_path)
	var full_texture_path = normalize_scene_path(texture_path)

	if not validate_file_exists(full_scene_path, "scene"):
		return false

	if not validate_file_exists(full_texture_path, "texture"):
		return false

	# Load and instantiate the scene
	var scene = load(full_scene_path)
	if not scene:
		log_error("Failed to load scene: " + full_scene_path)
		return false

	var scene_root = scene.instantiate()
	if not scene_root:
		log_error("Failed to instantiate scene")
		return false

	# Find the target node
	var target_node = find_node_by_path(scene_root, node_path)
	if not target_node:
		log_error("Target node not found: " + node_path)
		return false

	if not target_node is Sprite2D:
		log_error("Target node is not a Sprite2D: " + node_path)
		return false

	# Load the texture
	var texture = load(full_texture_path)
	if not texture:
		log_error("Failed to load texture: " + full_texture_path)
		return false

	# Set the texture
	target_node.texture = texture
	log_debug("Texture loaded successfully")

	# Save the modified scene
	return save_scene_instance(scene_root, full_scene_path, scene_path)

# Save a scene (with optional new path)
func save_scene(params: Dictionary) -> bool:
	if not validate_required_params(params, ["scene_path"]):
		return false

	var scene_path = params.scene_path
	var new_path = params.get("new_path", "")

	log_info("Saving scene: " + scene_path)

	var full_scene_path = normalize_scene_path(scene_path)

	if not validate_file_exists(full_scene_path, "scene"):
		return false

	var target_path = full_scene_path
	if not new_path.is_empty():
		target_path = normalize_scene_path(new_path)
		log_debug("Saving to new path: " + target_path)

		if not ensure_scene_directory_exists(target_path):
			return false

	# Load the scene
	var scene = load(full_scene_path)
	if not scene:
		log_error("Failed to load scene: " + full_scene_path)
		return false

	# Save the scene
	var save_error = ResourceSaver.save(scene, target_path)

	if save_error == OK:
		var display_path = new_path if not new_path.is_empty() else scene_path
		log_success("Scene saved successfully: " + display_path)
		return true
	else:
		log_error("Failed to save scene: " + str(save_error))
		return false

# ===== SCENE OPERATION HELPERS =====

# Ensure scene directory exists
func ensure_scene_directory_exists(scene_path: String) -> bool:
	var scene_dir = scene_path.get_base_dir()
	log_debug("Ensuring directory exists: " + scene_dir)

	var absolute_dir = get_absolute_path(scene_dir)
	log_debug("Absolute directory path: " + absolute_dir)

	if not DirAccess.dir_exists_absolute(absolute_dir):
		log_debug("Directory does not exist, creating: " + absolute_dir)
		var dir = DirAccess.open("res://")
		if dir:
			var error = dir.make_dir_recursive(scene_dir)
			if error == OK:
				log_debug("Directory created successfully")
				return true
			else:
				log_error("Failed to create directory: " + str(error))
				return false
		else:
			log_error("Failed to open res:// directory")
			return false
	else:
		log_debug("Directory already exists")
		return true

# Verify scene was created successfully
func verify_scene_created(scene_path: String) -> bool:
	if not FileAccess.file_exists(scene_path):
		log_error("Scene file was not created: " + scene_path)
		return false

	var absolute_path = get_absolute_path(scene_path)
	log_debug("Scene created at absolute path: " + absolute_path)
	return true

# Find node by path (supports "root" and "root/child/grandchild" syntax)
func find_node_by_path(scene_root: Node, node_path: String) -> Node:
	if node_path == "root":
		log_debug("Returning scene root")
		return scene_root

	if node_path.begins_with("root/"):
		var path_parts = node_path.split("/")
		path_parts.remove_at(0)  # Remove "root" part

		var current_node = scene_root
		for part in path_parts:
			log_debug("Looking for child node: " + part)
			current_node = current_node.get_node_or_null(part)
			if not current_node:
				log_error("Node not found in path: " + part)
				return null

		log_debug("Found node at path: " + node_path)
		return current_node
	else:
		log_error("Invalid node path format: " + node_path + " (should start with 'root')")
		return null

# Apply properties to a node
func apply_node_properties(node: Node, properties: Dictionary):
	if properties.is_empty():
		log_debug("No properties to apply")
		return

	log_debug("Applying " + str(properties.size()) + " properties to node")

	for property_name in properties:
		var property_value = properties[property_name]
		log_debug("Setting property: " + property_name + " = " + str(property_value))

		if node.has_method("set_" + property_name):
			node.call("set_" + property_name, property_value)
		elif property_name in node:
			node.set(property_name, property_value)
		else:
			log_warn("Property not found on node: " + property_name)

# Save a scene instance
func save_scene_instance(scene_root: Node, full_scene_path: String, display_path: String) -> bool:
	var new_scene = PackedScene.new()
	var pack_result = new_scene.pack(scene_root)

	if pack_result != OK:
		log_error("Failed to pack modified scene: " + str(pack_result))
		return false

	var save_error = ResourceSaver.save(new_scene, full_scene_path)

	if save_error == OK:
		log_success("Scene modified and saved successfully: " + display_path)
		return true
	else:
		log_error("Failed to save modified scene: " + str(save_error))
		return false

# ===== RESOURCE OPERATIONS =====

# Export a scene as a MeshLibrary resource
func export_mesh_library(params: Dictionary) -> bool:
	if not validate_required_params(params, ["scene_path", "output_path"]):
		return false

	var scene_path = params.scene_path
	var output_path = params.output_path
	var mesh_item_names = params.get("mesh_item_names", [])

	log_info("Exporting MeshLibrary from scene: " + scene_path)

	var full_scene_path = normalize_scene_path(scene_path)
	var full_output_path = normalize_scene_path(output_path)

	log_debug("Full scene path: " + full_scene_path)
	log_debug("Full output path: " + full_output_path)

	if not validate_file_exists(full_scene_path, "scene"):
		return false

	# Load and instantiate the scene
	var scene = load(full_scene_path)
	if not scene:
		log_error("Failed to load scene: " + full_scene_path)
		return false

	var scene_root = scene.instantiate()
	if not scene_root:
		log_error("Failed to instantiate scene")
		return false

	log_debug("Scene loaded and instantiated successfully")

	# Create MeshLibrary and process nodes
	var mesh_library = MeshLibrary.new()
	var item_count = process_mesh_nodes(scene_root, mesh_library, mesh_item_names)

	if item_count == 0:
		log_error("No valid meshes found in the scene")
		return false

	# Ensure output directory exists
	var output_dir = full_output_path.get_base_dir()
	if not ensure_scene_directory_exists(output_dir + "/dummy.txt"):  # Hack to create directory
		return false

	# Save the mesh library
	log_debug("Saving MeshLibrary to: " + full_output_path)
	var save_error = ResourceSaver.save(mesh_library, full_output_path)

	if save_error == OK:
		if FileAccess.file_exists(full_output_path):
			log_success("MeshLibrary exported successfully with " + str(item_count) + " items to: " + output_path)
			var absolute_path = get_absolute_path(full_output_path)
			log_debug("Absolute file path: " + absolute_path)
			return true
		else:
			log_error("File reported as saved but does not exist")
			return false
	else:
		log_error("Failed to save MeshLibrary: " + str(save_error))
		return false

# Get UID for a specific file
func get_uid(params: Dictionary) -> bool:
	if not validate_required_params(params, ["file_path"]):
		return false

	var file_path = params.file_path
	log_info("Getting UID for file: " + file_path)

	var full_file_path = normalize_scene_path(file_path)
	log_debug("Full file path: " + full_file_path)

	var absolute_path = get_absolute_path(full_file_path)
	log_debug("Absolute file path: " + absolute_path)

	if not validate_file_exists(full_file_path, "file"):
		return false

	# Check for UID file
	var uid_path = full_file_path + ".uid"
	log_debug("UID file path: " + uid_path)

	var uid_file = FileAccess.open(uid_path, FileAccess.READ)
	var result = {}

	if uid_file:
		var uid_content = uid_file.get_as_text()
		uid_file.close()

		result = {
			"file": full_file_path,
			"absolutePath": absolute_path,
			"uid": uid_content.strip_edges(),
			"exists": true
		}
		log_debug("UID found: " + uid_content.strip_edges())
	else:
		result = {
			"file": full_file_path,
			"absolutePath": absolute_path,
			"exists": false,
			"message": "UID file does not exist for this file. Use resave_resources to generate UIDs."
		}
		log_debug("UID file does not exist")

	# Output result as JSON
	var json_result = JSON.stringify(result)
	log_debug("UID result: " + json_result)
	print(json_result)

	return true

# Resave all resources to update UID references
func resave_resources(params: Dictionary) -> bool:
	log_info("Resaving all resources to update UID references...")

	var project_path = params.get("project_path", "res://")
	if not project_path.begins_with("res://"):
		project_path = "res://" + project_path
	if not project_path.ends_with("/"):
		project_path += "/"

	log_debug("Using project path: " + project_path)

	# Process scenes
	var scene_stats = resave_scenes(project_path)

	# Process scripts and shaders
	var script_stats = resave_scripts_and_shaders(project_path)

	# Log summary
	log_debug("=== Resave Summary ===")
	log_debug("Scenes processed: " + str(scene_stats.total))
	log_debug("Scenes successfully saved: " + str(scene_stats.success))
	log_debug("Scenes with errors: " + str(scene_stats.errors))
	log_debug("Scripts/shaders missing UIDs: " + str(script_stats.missing))
	log_debug("UIDs successfully generated: " + str(script_stats.generated))

	log_success("Resave operation complete")
	return true

# ===== RESOURCE OPERATION HELPERS =====

# Load helper functions from separate file
const Helpers = preload("res://scripts/godot_operations_helpers.gd")

# Process mesh nodes and add them to MeshLibrary
func process_mesh_nodes(scene_root: Node, mesh_library: MeshLibrary, mesh_item_names: Array) -> int:
	return Helpers.process_mesh_nodes(scene_root, mesh_library, mesh_item_names)

# Find files recursively with specific extension
func find_files(path: String, extension: String) -> Array:
	return Helpers.find_files(path, extension)

# Resave all scene files
func resave_scenes(project_path: String) -> Dictionary:
	return Helpers.resave_scenes(project_path)

# Resave scripts and shaders to generate UIDs
func resave_scripts_and_shaders(project_path: String) -> Dictionary:
	return Helpers.resave_scripts_and_shaders(project_path)

#!/usr/bin/env -S godot --headless --script
# Godot持久化代理 V2 - 使用文件通信而不是stdin
extends SceneTree

# 全局状态
var debug_mode: bool = false
var current_project_path: String = ""
var loaded_scenes: Dictionary = {}
var agent_running: bool = true
var command_file_path: String = ""
var response_file_path: String = ""

func _init():
	var args = OS.get_cmdline_args()
	debug_mode = "--debug-godot" in args
	
	# 解析命令行参数
	parse_arguments(args)
	
	# 输出启动信号
	write_response({
		"type": "agent_started",
		"message": "Godot持久化代理V2已启动",
		"timestamp": Time.get_unix_time_from_system(),
		"command_file": command_file_path,
		"response_file": response_file_path
	})
	
	# 开始监听循环
	start_command_loop()

func parse_arguments(args: Array):
	"""解析命令行参数"""
	for i in range(args.size()):
		if args[i] == "--command-file" and i + 1 < args.size():
			command_file_path = args[i + 1]
		elif args[i] == "--response-file" and i + 1 < args.size():
			response_file_path = args[i + 1]
	
	# 设置默认路径
	if command_file_path == "":
		command_file_path = "godot_agent_commands.json"
	if response_file_path == "":
		response_file_path = "godot_agent_responses.json"

func start_command_loop():
	"""开始命令监听循环"""
	log_debug("开始命令监听循环")
	
	while agent_running:
		# 检查命令文件
		if FileAccess.file_exists(command_file_path):
			process_command_file()
		
		# 短暂休眠
		await process_frame
		await process_frame  # 额外的帧等待

func process_command_file():
	"""处理命令文件"""
	var file = FileAccess.open(command_file_path, FileAccess.READ)
	if not file:
		log_debug("无法打开命令文件: " + command_file_path)
		return
	
	var content = file.get_as_text()
	file.close()
	
	# 删除命令文件
	DirAccess.remove_absolute(command_file_path)
	
	if content.strip_edges() == "":
		return
	
	log_debug("处理命令: " + content)
	process_command(content)

func process_command(command_json: String):
	"""处理JSON命令"""
	var json = JSON.new()
	var parse_result = json.parse(command_json)
	
	if parse_result != OK:
		write_response({
			"type": "error",
			"message": "JSON解析失败",
			"error": json.get_error_message()
		})
		return
	
	var command = json.data
	if not command is Dictionary:
		write_response({
			"type": "error",
			"message": "命令必须是JSON对象"
		})
		return
	
	# 处理不同类型的命令
	match command.get("action", ""):
		"ping":
			handle_ping(command)
		"load_project":
			handle_load_project(command)
		"query_scene":
			handle_query_scene(command)
		"modify_node":
			handle_modify_node(command)
		"create_scene":
			handle_create_scene(command)
		"save_scene":
			handle_save_scene(command)
		"shutdown":
			handle_shutdown(command)
		_:
			write_response({
				"type": "error",
				"message": "未知命令: " + str(command.get("action", ""))
			})

func handle_ping(command: Dictionary):
	"""处理ping命令"""
	write_response({
		"type": "pong",
		"message": "代理运行正常",
		"timestamp": Time.get_unix_time_from_system()
	})

func handle_load_project(command: Dictionary):
	"""加载项目"""
	var project_path = command.get("project_path", "")
	if project_path == "":
		write_response({
			"type": "error",
			"message": "缺少project_path参数"
		})
		return
	
	current_project_path = project_path
	write_response({
		"type": "project_loaded",
		"project_path": project_path,
		"message": "项目加载成功"
	})

func handle_create_scene(command: Dictionary):
	"""创建新场景"""
	var scene_path = command.get("scene_path", "")
	var root_type = command.get("root_type", "Node2D")
	
	if scene_path == "":
		write_response({
			"type": "error",
			"message": "缺少scene_path参数"
		})
		return
	
	# 创建新场景
	var root_node = ClassDB.instantiate(root_type)
	if not root_node:
		write_response({
			"type": "error",
			"message": "无法创建根节点类型: " + root_type
		})
		return
	
	root_node.name = "Root"
	
	# 创建PackedScene
	var packed_scene = PackedScene.new()
	packed_scene.pack(root_node)
	
	# 缓存场景
	loaded_scenes[scene_path] = packed_scene
	
	write_response({
		"type": "scene_created",
		"scene_path": scene_path,
		"root_type": root_type,
		"message": "场景创建成功"
	})
	
	# 清理临时节点
	root_node.queue_free()

func handle_query_scene(command: Dictionary):
	"""查询场景信息"""
	var scene_path = command.get("scene_path", "")
	if scene_path == "":
		write_response({
			"type": "error",
			"message": "缺少scene_path参数"
		})
		return
	
	# 加载场景
	var scene = load_scene_cached(scene_path)
	if not scene:
		write_response({
			"type": "error",
			"message": "无法加载场景: " + scene_path
		})
		return
	
	# 查询场景信息
	var scene_info = analyze_scene(scene)
	write_response({
		"type": "scene_info",
		"scene_path": scene_path,
		"data": scene_info
	})

func handle_modify_node(command: Dictionary):
	"""修改节点属性"""
	var scene_path = command.get("scene_path", "")
	var node_path = command.get("node_path", "")
	var properties = command.get("properties", {})
	
	if scene_path == "" or node_path == "":
		write_response({
			"type": "error",
			"message": "缺少scene_path或node_path参数"
		})
		return
	
	write_response({
		"type": "node_modified",
		"scene_path": scene_path,
		"node_path": node_path,
		"message": "节点修改功能开发中"
	})

func handle_save_scene(command: Dictionary):
	"""保存场景"""
	var scene_path = command.get("scene_path", "")
	
	if scene_path == "":
		write_response({
			"type": "error",
			"message": "缺少scene_path参数"
		})
		return
	
	var scene = loaded_scenes.get(scene_path)
	if not scene:
		write_response({
			"type": "error",
			"message": "场景未加载: " + scene_path
		})
		return
	
	# 保存场景
	var result = ResourceSaver.save(scene, scene_path)
	if result == OK:
		write_response({
			"type": "scene_saved",
			"scene_path": scene_path,
			"message": "场景保存成功"
		})
	else:
		write_response({
			"type": "error",
			"message": "保存场景失败: " + scene_path
		})

func handle_shutdown(command: Dictionary):
	"""关闭代理"""
	write_response({
		"type": "shutdown",
		"message": "代理正在关闭"
	})
	agent_running = false
	quit()

func load_scene_cached(scene_path: String) -> PackedScene:
	"""加载场景（带缓存）"""
	if scene_path in loaded_scenes:
		return loaded_scenes[scene_path]
	
	var scene = load(scene_path)
	if scene and scene is PackedScene:
		loaded_scenes[scene_path] = scene
		return scene
	
	return null

func analyze_scene(packed_scene: PackedScene) -> Dictionary:
	"""分析场景结构"""
	var instance = packed_scene.instantiate()
	if not instance:
		return {"error": "无法实例化场景"}
	
	var analysis = analyze_node_recursive(instance, "")
	instance.queue_free()
	
	return analysis

func analyze_node_recursive(node: Node, path_prefix: String) -> Dictionary:
	"""递归分析节点"""
	var node_path = path_prefix + "/" + node.name if path_prefix != "" else node.name
	
	var node_info = {
		"name": node.name,
		"type": node.get_class(),
		"path": node_path,
		"properties": get_node_properties(node),
		"children": []
	}
	
	# 分析子节点
	for child in node.get_children():
		node_info.children.append(analyze_node_recursive(child, node_path))
	
	return node_info

func get_node_properties(node: Node) -> Dictionary:
	"""获取节点的重要属性"""
	var properties = {}
	var important_props = ["position", "rotation", "scale", "visible", "modulate", "text", "texture"]
	
	for prop_name in important_props:
		if prop_name in node:
			properties[prop_name] = {
				"value": var_to_str(node.get(prop_name)),
				"type": typeof(node.get(prop_name))
			}
	
	return properties

func write_response(data: Dictionary):
	"""写入响应到文件"""
	var json_string = JSON.stringify(data)
	
	var file = FileAccess.open(response_file_path, FileAccess.WRITE)
	if file:
		file.store_string(json_string)
		file.close()
	else:
		print("ERROR: 无法写入响应文件: " + response_file_path)

func log_debug(message: String):
	"""调试日志"""
	if debug_mode:
		write_response({
			"type": "debug",
			"message": message,
			"timestamp": Time.get_unix_time_from_system()
		})

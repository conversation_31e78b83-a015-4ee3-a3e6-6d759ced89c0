#!/usr/bin/env -S godot --headless --script
# GDScript Operation Router Module
# Routes operations to appropriate handler modules

class_name <PERSON>otOperationRouter

# Import all operation modules
const GodotSceneOperations = preload("res://scripts/operations/scene_operations.gd")
const GodotResourceOperations = preload("res://scripts/operations/resource_operations.gd")

# Route operation to appropriate handler
static func route_operation(operation: String, params: Dictionary) -> bool:
	GodotLogger.log_operation_start(operation)
	
	var success = false
	
	match operation:
		# Scene operations
		"create_scene":
			success = GodotSceneOperations.create_scene(params)
		"add_node":
			success = GodotSceneOperations.add_node(params)
		"load_sprite":
			success = GodotSceneOperations.load_sprite(params)
		"save_scene":
			success = GodotSceneOperations.save_scene(params)

		# Resource operations
		"export_mesh_library":
			success = GodotResourceOperations.export_mesh_library(params)
		"get_uid":
			success = GodotResourceOperations.get_uid(params)
		"resave_resources":
			success = GodotResourceOperations.resave_resources(params)
		
		_:
			GodotLogger.log_error("Unknown operation: " + operation)
			return false
	
	if success:
		GodotLogger.log_operation_complete(operation)
	else:
		GodotLogger.log_operation_error(operation, "Operation failed")
	
	return success

# Get list of supported operations
static func get_supported_operations() -> Array:
	return [
		"create_scene",
		"add_node", 
		"load_sprite",
		"save_scene",
		"export_mesh_library",
		"get_uid",
		"resave_resources"
	]

# Check if operation is supported
static func is_operation_supported(operation: String) -> bool:
	return operation in get_supported_operations()

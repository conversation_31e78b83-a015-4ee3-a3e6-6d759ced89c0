/**
 * Editor-related tools for Godot MCP Server
 */

import { spawn } from 'child_process';
import { MCPTool, OperationParams, OperationResult } from '../core/types.js';
import { ParameterValidator } from '../utils/validation.js';
import { GodotOperationExecutor } from '../godot/operation-executor.js';
import { Logger } from '../utils/logger.js';

/**
 * Launch Godot editor for a specific project
 */
async function launchEditor(this: { operationExecutor: GodotOperationExecutor; logger: any }, params: OperationParams): Promise<OperationResult> {
  try {
    const { projectPath } = params;

    // Validate project path
    if (!ParameterValidator.validateProjectPath(projectPath)) {
      return {
        success: false,
        error: `Invalid project path: ${projectPath}`,
      };
    }

    // Get Godot path from the operation executor
    const stats = this.operationExecutor.getExecutorStats();
    if (!stats.hasGodotPath || !stats.godotPath) {
      return {
        success: false,
        error: 'Godot executable not found',
      };
    }

    this.logger.info(`Launching Godot editor for project: ${projectPath}`);

    // Launch Godot editor (non-blocking)
    const editorProcess = spawn(stats.godotPath, ['--path', projectPath], {
      detached: true,
      stdio: 'ignore',
    });

    // Unref the process so it doesn't keep the parent alive
    editorProcess.unref();

    this.logger.info('Godot editor launched successfully');

    return {
      success: true,
      data: {
        message: 'Godot editor launched successfully',
        projectPath,
        pid: editorProcess.pid,
      },
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    this.logger.error(`Failed to launch editor: ${errorMessage}`);
    
    return {
      success: false,
      error: errorMessage,
    };
  }
}

/**
 * Run a Godot project
 */
async function runProject(this: { operationExecutor: GodotOperationExecutor; logger: any }, params: OperationParams): Promise<OperationResult> {
  try {
    const { projectPath, scene } = params;

    // Validate project path
    if (!ParameterValidator.validateProjectPath(projectPath)) {
      return {
        success: false,
        error: `Invalid project path: ${projectPath}`,
      };
    }

    // Validate scene path if provided
    if (scene && !ParameterValidator.validateScenePath(scene, projectPath)) {
      return {
        success: false,
        error: `Invalid scene path: ${scene}`,
      };
    }

    this.logger.info(`Running Godot project: ${projectPath}${scene ? ` with scene: ${scene}` : ''}`);

    // Get Godot path from the operation executor
    const stats = this.operationExecutor.getExecutorStats();
    if (!stats.hasGodotPath || !stats.godotPath) {
      return {
        success: false,
        error: 'Godot executable not found',
      };
    }

    // Build command arguments
    const args = ['--path', projectPath];
    if (scene) {
      args.push(scene);
    }

    // Launch Godot project (non-blocking)
    const projectProcess = spawn(stats.godotPath, args, {
      detached: true,
      stdio: 'ignore',
    });

    // Unref the process so it doesn't keep the parent alive
    projectProcess.unref();

    this.logger.info('Godot project started successfully');

    return {
      success: true,
      data: {
        message: 'Godot project started successfully',
        projectPath,
        scene: scene || null,
        pid: projectProcess.pid,
      },
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    this.logger.error(`Failed to run project: ${errorMessage}`);
    
    return {
      success: false,
      error: errorMessage,
    };
  }
}

/**
 * Get debug output from the currently running process
 */
async function getDebugOutput(this: { operationExecutor: GodotOperationExecutor; logger: any }, params: OperationParams): Promise<OperationResult> {
  try {
    // This is a placeholder implementation
    // In the original code, this would get output from the active process
    this.logger.info('Getting debug output');

    return {
      success: true,
      data: {
        message: 'Debug output retrieval not implemented in modular version',
        output: [],
        errors: [],
      },
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    this.logger.error(`Failed to get debug output: ${errorMessage}`);
    
    return {
      success: false,
      error: errorMessage,
    };
  }
}

/**
 * Stop the currently running project
 */
async function stopProject(this: { operationExecutor: GodotOperationExecutor; logger: any }, params: OperationParams): Promise<OperationResult> {
  try {
    this.logger.info('Stopping project');

    // This is a placeholder implementation
    // In the original code, this would stop the active process
    return {
      success: true,
      data: {
        message: 'Project stop not implemented in modular version',
      },
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    this.logger.error(`Failed to stop project: ${errorMessage}`);
    
    return {
      success: false,
      error: errorMessage,
    };
  }
}

/**
 * Get Godot version information
 */
async function getGodotVersion(this: { operationExecutor: GodotOperationExecutor; logger: any }, params: OperationParams): Promise<OperationResult> {
  try {
    this.logger.info('Getting Godot version');

    const stats = this.operationExecutor.getExecutorStats();
    if (!stats.hasGodotPath || !stats.godotPath) {
      return {
        success: false,
        error: 'Godot executable not found',
      };
    }

    // Execute Godot with --version flag
    const { exec } = await import('child_process');
    const { promisify } = await import('util');
    const execAsync = promisify(exec);

    const { stdout, stderr } = await execAsync(`"${stats.godotPath}" --version`, { timeout: 10000 });
    const version = (stdout + stderr).trim();

    this.logger.info(`Godot version: ${version}`);

    return {
      success: true,
      data: {
        version,
        godotPath: stats.godotPath,
      },
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    this.logger.error(`Failed to get Godot version: ${errorMessage}`);
    
    return {
      success: false,
      error: errorMessage,
    };
  }
}

/**
 * Editor tools definition
 */
export const editorTools: MCPTool[] = [
  {
    name: 'launch_editor',
    description: 'Launch Godot editor for a specific project',
    inputSchema: {
      type: 'object',
      properties: {
        projectPath: {
          type: 'string',
          description: 'Path to the Godot project directory',
        },
      },
      required: ['projectPath'],
    },
    handler: launchEditor,
  },
  {
    name: 'run_project',
    description: 'Run the Godot project and capture output',
    inputSchema: {
      type: 'object',
      properties: {
        projectPath: {
          type: 'string',
          description: 'Path to the Godot project directory',
        },
        scene: {
          type: 'string',
          description: 'Optional: Specific scene to run',
        },
      },
      required: ['projectPath'],
    },
    handler: runProject,
  },
  {
    name: 'get_debug_output',
    description: 'Get the current debug output and errors',
    inputSchema: {
      type: 'object',
      properties: {},
      required: [],
    },
    handler: getDebugOutput,
  },
  {
    name: 'stop_project',
    description: 'Stop the currently running Godot project',
    inputSchema: {
      type: 'object',
      properties: {},
      required: [],
    },
    handler: stopProject,
  },
  {
    name: 'get_godot_version',
    description: 'Get the installed Godot version',
    inputSchema: {
      type: 'object',
      properties: {},
      required: [],
    },
    handler: getGodotVersion,
  },
];

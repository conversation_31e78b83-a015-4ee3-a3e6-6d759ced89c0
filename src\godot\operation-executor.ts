/**
 * Godot operation execution
 */

import { join, dirname } from 'path';
import { fileURLToPath } from 'url';
import { OperationParams, OperationResult } from '../core/types.js';
import { ConfigManager } from '../core/config.js';
import { Logger } from '../utils/logger.js';
import { ParameterValidator } from '../utils/validation.js';
import { PathUtils } from '../utils/path-utils.js';
import { GodotPathDetector } from './path-detector.js';
import { GodotProcessManager } from './process-manager.js';

// ES module equivalent of __dirname
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const logger = Logger.createScopedLogger('OperationExecutor');

/**
 * Godot operation executor class
 */
export class GodotOperationExecutor {
  private configManager: ConfigManager;
  private pathDetector: GodotPathDetector;
  private processManager: GodotProcessManager;
  private operationsScriptPath: string;

  constructor(pathDetector: GodotPathDetector, processManager: GodotProcessManager) {
    this.configManager = ConfigManager.getInstance();
    this.pathDetector = pathDetector;
    this.processManager = processManager;
    
    // Set the path to the operations script
    this.operationsScriptPath = join(__dirname, '..', 'scripts', 'godot_operations.gd');
    logger.debug(`Operations script path: ${this.operationsScriptPath}`);
  }

  /**
   * Execute a Godot operation
   */
  public async executeOperation(
    operation: string, 
    params: OperationParams, 
    projectPath: string
  ): Promise<OperationResult> {
    try {
      logger.info(`Executing operation: ${operation}`);
      logger.debug(`Project path: ${projectPath}`);
      
      // Validate project path
      if (!ParameterValidator.validateProjectPath(projectPath)) {
        return {
          success: false,
          error: `Invalid project path: ${projectPath}`,
        };
      }

      // Get Godot path
      const godotPath = await this.pathDetector.detectGodotPath();
      if (!godotPath) {
        return {
          success: false,
          error: 'Could not find Godot executable',
        };
      }

      // Validate operation parameters
      const validation = ParameterValidator.validateOperationParams(operation, params);
      if (!validation.valid) {
        return {
          success: false,
          error: `Parameter validation failed: ${validation.errors.join(', ')}`,
        };
      }

      // Convert parameters to snake_case for Godot
      const snakeCaseParams = ParameterValidator.convertCamelToSnakeCase(params);
      
      // Execute the operation
      const result = await this.executeGodotCommand(
        godotPath,
        projectPath,
        operation,
        snakeCaseParams
      );

      logger.info(`Operation ${operation} completed ${result.success ? 'successfully' : 'with errors'}`);
      return result;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logger.error(`Operation ${operation} failed: ${errorMessage}`);
      
      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  /**
   * Execute Godot command with parameters
   */
  private async executeGodotCommand(
    godotPath: string,
    projectPath: string,
    operation: string,
    params: OperationParams
  ): Promise<OperationResult> {
    try {
      // Serialize parameters to JSON
      const paramsJson = JSON.stringify(params);
      // Escape single quotes to prevent command injection
      const escapedParams = paramsJson.replace(/'/g, "'\\''");

      // Add debug arguments if debug mode is enabled
      const debugArgs = this.configManager.isGodotDebugMode() ? ['--debug-godot'] : [];

      // Construct the command
      const command = [
        godotPath,
        '--headless',
        '--path',
        projectPath,
        '--script',
        this.operationsScriptPath,
        operation,
        escapedParams,
        ...debugArgs,
      ];

      logger.debug(`Executing command: ${command.join(' ')}`);

      // Execute the process
      const process = await this.processManager.startProcess(command, {
        cwd: projectPath,
        timeout: 30000, // 30 second timeout
      });

      // Wait for process to complete
      await this.processManager.waitForProcess(process, 30000);

      // Get process output
      const { stdout, stderr, combined } = this.processManager.getProcessOutput(process);

      // Parse the result
      const result = this.parseOperationResult(combined, stdout, stderr);
      
      logger.debug(`Operation result: ${JSON.stringify(result)}`);
      return result;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logger.error(`Command execution failed: ${errorMessage}`);
      
      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  /**
   * Parse operation result from Godot output
   */
  private parseOperationResult(combined: string, stdout: string, stderr: string): OperationResult {
    // Check for common error patterns
    const errorPatterns = [
      /\[ERROR\]/i,
      /Error:/i,
      /Failed to/i,
      /Cannot/i,
      /Invalid/i,
    ];

    const hasErrors = errorPatterns.some(pattern => pattern.test(combined));
    
    // Check for success indicators
    const successPatterns = [
      /\[INFO\].*completed/i,
      /\[INFO\].*success/i,
      /Operation completed/i,
    ];

    const hasSuccess = successPatterns.some(pattern => pattern.test(combined));

    // Determine success based on patterns and exit behavior
    const success = !hasErrors && (hasSuccess || !stderr.trim());

    const result: OperationResult = {
      success,
      output: combined.split('\n').filter(line => line.trim()),
    };

    if (!success) {
      // Extract error message
      const errorLines = combined
        .split('\n')
        .filter(line => errorPatterns.some(pattern => pattern.test(line)))
        .map(line => line.replace(/^\[ERROR\]\s*/i, '').trim())
        .filter(line => line);

      result.error = errorLines.length > 0 
        ? errorLines.join('; ') 
        : 'Operation failed with unknown error';
    }

    return result;
  }

  /**
   * Get the operations script path
   */
  public getOperationsScriptPath(): string {
    return this.operationsScriptPath;
  }

  /**
   * Set a custom operations script path (useful for testing)
   */
  public setOperationsScriptPath(path: string): void {
    this.operationsScriptPath = path;
    logger.debug(`Operations script path updated: ${path}`);
  }

  /**
   * Validate that the operations script exists
   */
  public validateOperationsScript(): boolean {
    const exists = PathUtils.pathExists(this.operationsScriptPath);
    if (!exists) {
      logger.error(`Operations script not found: ${this.operationsScriptPath}`);
    }
    return exists;
  }

  /**
   * Get executor statistics
   */
  public getExecutorStats(): {
    operationsScriptPath: string;
    operationsScriptExists: boolean;
    hasGodotPath: boolean;
    godotPath?: string;
  } {
    return {
      operationsScriptPath: this.operationsScriptPath,
      operationsScriptExists: this.validateOperationsScript(),
      hasGodotPath: !!this.pathDetector.getCachedPath(),
      godotPath: this.pathDetector.getCachedPath() || undefined,
    };
  }
}

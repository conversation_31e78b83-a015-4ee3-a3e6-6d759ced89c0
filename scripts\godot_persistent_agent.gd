#!/usr/bin/env -S godot --headless --script
# Godot持久化代理 - 支持长时间运行和JSON协议通信
extends SceneTree

# 全局状态
var debug_mode: bool = false
var current_project_path: String = ""
var loaded_scenes: Dictionary = {}  # 场景缓存
var agent_running: bool = true

# 通信相关
var stdin_buffer: String = ""

func _init():
	print_json_response({"type": "agent_started", "message": "Godot持久化代理已启动", "timestamp": Time.get_unix_time_from_system()})
	
	# 设置主循环
	set_auto_accept_quit(false)
	
	# 开始监听输入
	start_input_loop()

func start_input_loop():
	"""开始输入监听循环"""
	# 使用文件读取方式处理stdin
	var stdin_file = FileAccess.open("/dev/stdin", FileAccess.READ)
	if not stdin_file:
		# Windows系统尝试其他方式
		print_json_response({
			"type": "warning",
			"message": "无法直接读取stdin，使用轮询模式"
		})

	while agent_running:
		var input = ""

		# 尝试读取输入
		if stdin_file and not stdin_file.eof_reached():
			input = stdin_file.get_line()

		if input != "":
			process_command(input)

		# 短暂休眠避免CPU占用过高
		await get_process_frame()

	if stdin_file:
		stdin_file.close()

func read_stdin_line() -> String:
	"""读取标准输入的一行 - 已废弃，由start_input_loop直接处理"""
	return ""

func process_command(command_json: String):
	"""处理接收到的JSON命令"""
	var json = JSON.new()
	var parse_result = json.parse(command_json)
	
	if parse_result != OK:
		print_json_response({
			"type": "error",
			"message": "JSON解析失败",
			"error": json.get_error_message()
		})
		return
	
	var command = json.data
	if not command is Dictionary:
		print_json_response({
			"type": "error", 
			"message": "命令必须是JSON对象"
		})
		return
	
	# 处理不同类型的命令
	match command.get("action", ""):
		"ping":
			handle_ping(command)
		"load_project":
			handle_load_project(command)
		"query_scene":
			handle_query_scene(command)
		"modify_node":
			handle_modify_node(command)
		"create_scene":
			handle_create_scene(command)
		"save_scene":
			handle_save_scene(command)
		"shutdown":
			handle_shutdown(command)
		_:
			print_json_response({
				"type": "error",
				"message": "未知命令: " + str(command.get("action", ""))
			})

func handle_ping(command: Dictionary):
	"""处理ping命令"""
	print_json_response({
		"type": "pong",
		"message": "代理运行正常",
		"timestamp": Time.get_unix_time_from_system()
	})

func handle_load_project(command: Dictionary):
	"""加载项目"""
	var project_path = command.get("project_path", "")
	if project_path == "":
		print_json_response({
			"type": "error",
			"message": "缺少project_path参数"
		})
		return
	
	current_project_path = project_path
	print_json_response({
		"type": "project_loaded",
		"project_path": project_path,
		"message": "项目加载成功"
	})

func handle_query_scene(command: Dictionary):
	"""查询场景信息"""
	var scene_path = command.get("scene_path", "")
	if scene_path == "":
		print_json_response({
			"type": "error",
			"message": "缺少scene_path参数"
		})
		return
	
	# 加载场景
	var scene = load_scene_cached(scene_path)
	if not scene:
		print_json_response({
			"type": "error",
			"message": "无法加载场景: " + scene_path
		})
		return
	
	# 查询场景信息
	var scene_info = analyze_scene(scene)
	print_json_response({
		"type": "scene_info",
		"scene_path": scene_path,
		"data": scene_info
	})

func handle_modify_node(command: Dictionary):
	"""修改节点属性"""
	var scene_path = command.get("scene_path", "")
	var node_path = command.get("node_path", "")
	var properties = command.get("properties", {})
	
	if scene_path == "" or node_path == "":
		print_json_response({
			"type": "error",
			"message": "缺少scene_path或node_path参数"
		})
		return
	
	# 加载场景
	var scene = load_scene_cached(scene_path)
	if not scene:
		print_json_response({
			"type": "error",
			"message": "无法加载场景: " + scene_path
		})
		return
	
	# 查找节点
	var node = scene.get_node_or_null(node_path)
	if not node:
		print_json_response({
			"type": "error",
			"message": "找不到节点: " + node_path
		})
		return
	
	# 修改属性
	var modified_properties = []
	for prop_name in properties:
		var prop_value = properties[prop_name]
		if node.has_method("set_" + prop_name) or prop_name in node:
			node.set(prop_name, prop_value)
			modified_properties.append(prop_name)
	
	print_json_response({
		"type": "node_modified",
		"scene_path": scene_path,
		"node_path": node_path,
		"modified_properties": modified_properties
	})

func handle_create_scene(command: Dictionary):
	"""创建新场景"""
	var scene_path = command.get("scene_path", "")
	var root_type = command.get("root_type", "Node2D")
	
	if scene_path == "":
		print_json_response({
			"type": "error",
			"message": "缺少scene_path参数"
		})
		return
	
	# 创建新场景
	var root_node = ClassDB.instantiate(root_type)
	if not root_node:
		print_json_response({
			"type": "error",
			"message": "无法创建根节点类型: " + root_type
		})
		return
	
	root_node.name = "Root"
	
	# 创建PackedScene
	var packed_scene = PackedScene.new()
	packed_scene.pack(root_node)
	
	# 缓存场景
	loaded_scenes[scene_path] = packed_scene
	
	print_json_response({
		"type": "scene_created",
		"scene_path": scene_path,
		"root_type": root_type
	})

func handle_save_scene(command: Dictionary):
	"""保存场景"""
	var scene_path = command.get("scene_path", "")
	
	if scene_path == "":
		print_json_response({
			"type": "error",
			"message": "缺少scene_path参数"
		})
		return
	
	var scene = loaded_scenes.get(scene_path)
	if not scene:
		print_json_response({
			"type": "error",
			"message": "场景未加载: " + scene_path
		})
		return
	
	# 保存场景
	var result = ResourceSaver.save(scene, scene_path)
	if result == OK:
		print_json_response({
			"type": "scene_saved",
			"scene_path": scene_path
		})
	else:
		print_json_response({
			"type": "error",
			"message": "保存场景失败: " + scene_path
		})

func handle_shutdown(command: Dictionary):
	"""关闭代理"""
	print_json_response({
		"type": "shutdown",
		"message": "代理正在关闭"
	})
	agent_running = false
	quit()

func load_scene_cached(scene_path: String) -> PackedScene:
	"""加载场景（带缓存）"""
	if scene_path in loaded_scenes:
		return loaded_scenes[scene_path]
	
	var scene = load(scene_path)
	if scene and scene is PackedScene:
		loaded_scenes[scene_path] = scene
		return scene
	
	return null

func analyze_scene(packed_scene: PackedScene) -> Dictionary:
	"""分析场景结构"""
	var instance = packed_scene.instantiate()
	if not instance:
		return {"error": "无法实例化场景"}
	
	var analysis = analyze_node_recursive(instance, "")
	instance.queue_free()
	
	return analysis

func analyze_node_recursive(node: Node, path_prefix: String) -> Dictionary:
	"""递归分析节点"""
	var node_path = path_prefix + "/" + node.name if path_prefix != "" else node.name
	
	var node_info = {
		"name": node.name,
		"type": node.get_class(),
		"path": node_path,
		"properties": get_node_properties(node),
		"children": []
	}
	
	# 分析子节点
	for child in node.get_children():
		node_info.children.append(analyze_node_recursive(child, node_path))
	
	return node_info

func get_node_properties(node: Node) -> Dictionary:
	"""获取节点的可编辑属性"""
	var properties = {}
	var prop_list = node.get_property_list()
	
	for prop in prop_list:
		if prop.usage & PROPERTY_USAGE_EDITOR and not (prop.usage & PROPERTY_USAGE_READ_ONLY):
			properties[prop.name] = {
				"type": prop.type,
				"value": node.get(prop.name)
			}
	
	return properties

func print_json_response(data: Dictionary):
	"""输出JSON响应"""
	var json_string = JSON.stringify(data)
	print(json_string)
	# 确保输出立即刷新
	OS.execute("", [], [], false)

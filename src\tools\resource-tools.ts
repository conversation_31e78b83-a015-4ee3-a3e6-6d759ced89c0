/**
 * Resource management tools for Godot MCP Server
 */

import { MCPTool, OperationParams, OperationResult } from '../core/types.js';
import { ParameterValidator } from '../utils/validation.js';
import { PathUtils } from '../utils/path-utils.js';
import { GodotOperationExecutor } from '../godot/operation-executor.js';

/**
 * Export a scene as a MeshLibrary resource
 */
async function exportMeshLibrary(this: { operationExecutor: GodotOperationExecutor; logger: any }, params: OperationParams): Promise<OperationResult> {
  try {
    const { projectPath, scenePath, outputPath, meshItemNames } = params;

    // Validate project path
    if (!ParameterValidator.validateProjectPath(projectPath)) {
      return {
        success: false,
        error: `Invalid project path: ${projectPath}`,
      };
    }

    // Validate scene path
    if (!ParameterValidator.validateScenePath(scenePath, projectPath)) {
      return {
        success: false,
        error: `Invalid scene path: ${scenePath}`,
      };
    }

    // Validate output path
    if (!outputPath || typeof outputPath !== 'string') {
      return {
        success: false,
        error: 'Output path is required',
      };
    }

    // Ensure output path has .res extension
    const normalizedOutputPath = PathUtils.ensureExtension(outputPath, '.res');

    this.logger.info(`Exporting mesh library from scene: ${scenePath} to: ${normalizedOutputPath}`);

    // Prepare operation parameters
    const operationParams: any = {
      scenePath,
      outputPath: normalizedOutputPath,
    };

    if (meshItemNames && Array.isArray(meshItemNames)) {
      operationParams.meshItemNames = meshItemNames;
    }

    // Execute the export_mesh_library operation
    const result = await this.operationExecutor.executeOperation(
      'export_mesh_library',
      operationParams,
      projectPath
    );

    if (result.success) {
      this.logger.info(`Mesh library exported successfully: ${normalizedOutputPath}`);
    } else {
      this.logger.error(`Failed to export mesh library: ${result.error}`);
    }

    return result;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    this.logger.error(`Failed to export mesh library: ${errorMessage}`);
    
    return {
      success: false,
      error: errorMessage,
    };
  }
}

/**
 * Get the UID for a specific file in a Godot project (for Godot 4.4+)
 */
async function getUid(this: { operationExecutor: GodotOperationExecutor; logger: any }, params: OperationParams): Promise<OperationResult> {
  try {
    const { projectPath, filePath } = params;

    // Validate project path
    if (!ParameterValidator.validateProjectPath(projectPath)) {
      return {
        success: false,
        error: `Invalid project path: ${projectPath}`,
      };
    }

    // Validate file path
    if (!filePath || typeof filePath !== 'string') {
      return {
        success: false,
        error: 'File path is required',
      };
    }

    // Validate that the file path is safe
    const sanitizedFilePath = ParameterValidator.sanitizePath(filePath);
    if (!sanitizedFilePath) {
      return {
        success: false,
        error: `Invalid file path: ${filePath}`,
      };
    }

    this.logger.info(`Getting UID for file: ${sanitizedFilePath}`);

    // Execute the get_uid operation
    const result = await this.operationExecutor.executeOperation(
      'get_uid',
      {
        filePath: sanitizedFilePath,
      },
      projectPath
    );

    if (result.success) {
      this.logger.info(`UID retrieved successfully for: ${sanitizedFilePath}`);
    } else {
      this.logger.error(`Failed to get UID: ${result.error}`);
    }

    return result;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    this.logger.error(`Failed to get UID: ${errorMessage}`);
    
    return {
      success: false,
      error: errorMessage,
    };
  }
}

/**
 * Resource tools definition
 */
export const resourceTools: MCPTool[] = [
  {
    name: 'export_mesh_library',
    description: 'Export a scene as a MeshLibrary resource',
    inputSchema: {
      type: 'object',
      properties: {
        projectPath: {
          type: 'string',
          description: 'Path to the Godot project directory',
        },
        scenePath: {
          type: 'string',
          description: 'Path to the scene file (.tscn) to export',
        },
        outputPath: {
          type: 'string',
          description: 'Path where the mesh library (.res) will be saved',
        },
        meshItemNames: {
          type: 'array',
          items: {
            type: 'string',
          },
          description: 'Optional: Names of specific mesh items to include (defaults to all)',
        },
      },
      required: ['projectPath', 'scenePath', 'outputPath'],
    },
    handler: exportMeshLibrary,
  },
  {
    name: 'get_uid',
    description: 'Get the UID for a specific file in a Godot project (for Godot 4.4+)',
    inputSchema: {
      type: 'object',
      properties: {
        projectPath: {
          type: 'string',
          description: 'Path to the Godot project directory',
        },
        filePath: {
          type: 'string',
          description: 'Path to the file (relative to project) for which to get the UID',
        },
      },
      required: ['projectPath', 'filePath'],
    },
    handler: getUid,
  },
];

/**
 * Project management tools for Godot MCP Server
 */

import { readdir, readFile } from 'fs/promises';
import { join, basename } from 'path';
import { MCPTool, OperationParams, OperationResult } from '../core/types.js';
import { ParameterValidator } from '../utils/validation.js';
import { PathUtils } from '../utils/path-utils.js';
import { GodotOperationExecutor } from '../godot/operation-executor.js';

/**
 * List Godot projects in a directory
 */
async function listProjects(this: { operationExecutor: GodotOperationExecutor; logger: any }, params: OperationParams): Promise<OperationResult> {
  try {
    const { directory, recursive = false } = params;

    if (!directory || typeof directory !== 'string') {
      return {
        success: false,
        error: 'Directory parameter is required',
      };
    }

    if (!PathUtils.pathExists(directory) || !PathUtils.isDirectory(directory)) {
      return {
        success: false,
        error: `Directory does not exist or is not a directory: ${directory}`,
      };
    }

    this.logger.info(`Listing Godot projects in: ${directory} (recursive: ${recursive})`);

    const projects = await findGodotProjects.call(this, directory, recursive);

    this.logger.info(`Found ${projects.length} Godot projects`);

    return {
      success: true,
      data: {
        directory,
        recursive,
        projects,
        count: projects.length,
      },
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    this.logger.error(`Failed to list projects: ${errorMessage}`);
    
    return {
      success: false,
      error: errorMessage,
    };
  }
}

/**
 * Helper function to find Godot projects
 */
async function findGodotProjects(this: any, directory: string, recursive: boolean): Promise<Array<{ name: string; path: string }>> {
  const projects: Array<{ name: string; path: string }> = [];

  try {
    const entries = await readdir(directory, { withFileTypes: true });

    for (const entry of entries) {
      const fullPath = join(directory, entry.name);

      if (entry.isDirectory()) {
        // Check if this directory is a Godot project
        if (PathUtils.looksLikeGodotProject(fullPath)) {
          projects.push({
            name: basename(fullPath),
            path: fullPath,
          });
        }

        // Recursively search subdirectories if requested
        if (recursive) {
          const subProjects = await this.findGodotProjects(fullPath, recursive);
          projects.push(...subProjects);
        }
      }
    }
  } catch (error) {
    this.logger.debug(`Error reading directory ${directory}: ${error}`);
  }

  return projects;
}

/**
 * Get information about a Godot project
 */
async function getProjectInfo(this: { operationExecutor: GodotOperationExecutor; logger: any }, params: OperationParams): Promise<OperationResult> {
  try {
    const { projectPath } = params;

    // Validate project path
    if (!ParameterValidator.validateProjectPath(projectPath)) {
      return {
        success: false,
        error: `Invalid project path: ${projectPath}`,
      };
    }

    this.logger.info(`Getting project info for: ${projectPath}`);

    const projectInfo = await parseProjectFile.call(this, projectPath);

    this.logger.info(`Retrieved project info for: ${projectInfo.name || 'Unknown'}`);

    return {
      success: true,
      data: projectInfo,
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    this.logger.error(`Failed to get project info: ${errorMessage}`);
    
    return {
      success: false,
      error: errorMessage,
    };
  }
}

/**
 * Helper function to parse project.godot file
 */
async function parseProjectFile(this: any, projectPath: string): Promise<any> {
  const projectFilePath = join(projectPath, 'project.godot');
  
  try {
    const content = await readFile(projectFilePath, 'utf-8');
    const projectInfo: any = {
      path: projectPath,
      name: basename(projectPath),
      projectFile: projectFilePath,
    };

    // Parse basic project information
    const lines = content.split('\n');
    
    for (const line of lines) {
      const trimmedLine = line.trim();
      
      // Parse config/name
      if (trimmedLine.startsWith('config/name=')) {
        const match = trimmedLine.match(/config\/name="([^"]+)"/);
        if (match) {
          projectInfo.name = match[1];
        }
      }
      
      // Parse config/description
      if (trimmedLine.startsWith('config/description=')) {
        const match = trimmedLine.match(/config\/description="([^"]+)"/);
        if (match) {
          projectInfo.description = match[1];
        }
      }
      
      // Parse config/version
      if (trimmedLine.startsWith('config/version=')) {
        const match = trimmedLine.match(/config\/version="([^"]+)"/);
        if (match) {
          projectInfo.version = match[1];
        }
      }
      
      // Parse main scene
      if (trimmedLine.startsWith('run/main_scene=')) {
        const match = trimmedLine.match(/run\/main_scene="([^"]+)"/);
        if (match) {
          projectInfo.mainScene = match[1];
        }
      }
    }

    // Get additional file system information
    try {
      const entries = await readdir(projectPath, { withFileTypes: true });
      
      projectInfo.structure = {
        directories: entries.filter(e => e.isDirectory()).map(e => e.name),
        files: entries.filter(e => e.isFile()).map(e => e.name),
      };
      
      // Count scene files
      const sceneFiles = entries
        .filter(e => e.isFile() && e.name.endsWith('.tscn'))
        .map(e => e.name);
      
      projectInfo.sceneCount = sceneFiles.length;
      projectInfo.scenes = sceneFiles;
      
      // Count script files
      const scriptFiles = entries
        .filter(e => e.isFile() && (e.name.endsWith('.gd') || e.name.endsWith('.cs')))
        .map(e => e.name);
      
      projectInfo.scriptCount = scriptFiles.length;
      projectInfo.scripts = scriptFiles;
      
    } catch (error) {
      this.logger.debug(`Error reading project structure: ${error}`);
    }

    return projectInfo;
  } catch (error) {
    this.logger.debug(`Error parsing project file: ${error}`);
    
    return {
      path: projectPath,
      name: basename(projectPath),
      error: 'Could not parse project.godot file',
    };
  }
}

/**
 * Update project UIDs (for Godot 4.4+)
 */
async function updateProjectUids(this: { operationExecutor: GodotOperationExecutor; logger: any }, params: OperationParams): Promise<OperationResult> {
  try {
    const { projectPath } = params;

    // Validate project path
    if (!ParameterValidator.validateProjectPath(projectPath)) {
      return {
        success: false,
        error: `Invalid project path: ${projectPath}`,
      };
    }

    this.logger.info(`Updating project UIDs for: ${projectPath}`);

    // Execute the resave_resources operation
    const result = await this.operationExecutor.executeOperation(
      'resave_resources',
      {},
      projectPath
    );

    if (result.success) {
      this.logger.info('Project UIDs updated successfully');
    } else {
      this.logger.error(`Failed to update project UIDs: ${result.error}`);
    }

    return result;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    this.logger.error(`Failed to update project UIDs: ${errorMessage}`);
    
    return {
      success: false,
      error: errorMessage,
    };
  }
}

/**
 * Project tools definition
 */
export const projectTools: MCPTool[] = [
  {
    name: 'list_projects',
    description: 'List Godot projects in a directory',
    inputSchema: {
      type: 'object',
      properties: {
        directory: {
          type: 'string',
          description: 'Directory to search for Godot projects',
        },
        recursive: {
          type: 'boolean',
          description: 'Whether to search recursively (default: false)',
        },
      },
      required: ['directory'],
    },
    handler: listProjects,
  },
  {
    name: 'get_project_info',
    description: 'Retrieve metadata about a Godot project',
    inputSchema: {
      type: 'object',
      properties: {
        projectPath: {
          type: 'string',
          description: 'Path to the Godot project directory',
        },
      },
      required: ['projectPath'],
    },
    handler: getProjectInfo,
  },
  {
    name: 'update_project_uids',
    description: 'Update UID references in a Godot project by resaving resources (for Godot 4.4+)',
    inputSchema: {
      type: 'object',
      properties: {
        projectPath: {
          type: 'string',
          description: 'Path to the Godot project directory',
        },
      },
      required: ['projectPath'],
    },
    handler: updateProjectUids,
  },
];



#!/usr/bin/env node
/**
 * 测试MCP服务器启动和工具注册
 */

import { GodotMCPServer } from './build/core/server.js';

async function testMCPServer() {
  try {
    console.log('开始测试MCP服务器...');
    
    // 创建服务器实例
    const server = new GodotMCPServer();
    
    // 获取服务器统计信息
    const stats = server.getServerStats();
    console.log('服务器统计信息:', JSON.stringify(stats, null, 2));
    
    console.log('MCP服务器测试完成');
    
  } catch (error) {
    console.error('测试失败:', error);
  }
}

// 运行测试
testMCPServer().catch(console.error);

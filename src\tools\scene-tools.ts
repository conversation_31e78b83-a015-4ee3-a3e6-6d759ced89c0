/**
 * Scene management tools for Godot MCP Server
 */

import { MCPTool, OperationParams, OperationResult } from '../core/types.js';
import { ParameterValidator } from '../utils/validation.js';
import { PathUtils } from '../utils/path-utils.js';
import { GodotOperationExecutor } from '../godot/operation-executor.js';

/**
 * Create a new Godot scene file
 */
async function createScene(this: { operationExecutor: GodotOperationExecutor; logger: any }, params: OperationParams): Promise<OperationResult> {
  try {
    const { projectPath, scenePath, rootNodeType = 'Node2D' } = params;

    // Validate project path
    if (!ParameterValidator.validateProjectPath(projectPath)) {
      return {
        success: false,
        error: `Invalid project path: ${projectPath}`,
      };
    }

    // Validate scene path
    if (!ParameterValidator.validateScenePath(scenePath, projectPath)) {
      return {
        success: false,
        error: `Invalid scene path: ${scenePath}`,
      };
    }

    // Validate root node type
    if (!ParameterValidator.validateNodeType(rootNodeType)) {
      return {
        success: false,
        error: `Invalid root node type: ${rootNodeType}`,
      };
    }

    this.logger.info(`Creating scene: ${scenePath} with root node type: ${rootNodeType}`);

    // Execute the create_scene operation
    const result = await this.operationExecutor.executeOperation(
      'create_scene',
      {
        scenePath,
        rootNodeType,
      },
      projectPath
    );

    if (result.success) {
      this.logger.info(`Scene created successfully: ${scenePath}`);
    } else {
      this.logger.error(`Failed to create scene: ${result.error}`);
    }

    return result;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    this.logger.error(`Failed to create scene: ${errorMessage}`);
    
    return {
      success: false,
      error: errorMessage,
    };
  }
}

/**
 * Add a node to an existing scene
 */
async function addNode(this: { operationExecutor: GodotOperationExecutor; logger: any }, params: OperationParams): Promise<OperationResult> {
  try {
    const { 
      projectPath, 
      scenePath, 
      nodeType, 
      nodeName, 
      parentNodePath = 'root',
      properties = {}
    } = params;

    // Validate project path
    if (!ParameterValidator.validateProjectPath(projectPath)) {
      return {
        success: false,
        error: `Invalid project path: ${projectPath}`,
      };
    }

    // Validate scene path
    if (!ParameterValidator.validateScenePath(scenePath, projectPath)) {
      return {
        success: false,
        error: `Invalid scene path: ${scenePath}`,
      };
    }

    // Validate node type
    if (!ParameterValidator.validateNodeType(nodeType)) {
      return {
        success: false,
        error: `Invalid node type: ${nodeType}`,
      };
    }

    // Validate node name
    if (!ParameterValidator.validateNodeName(nodeName)) {
      return {
        success: false,
        error: `Invalid node name: ${nodeName}`,
      };
    }

    this.logger.info(`Adding node ${nodeName} (${nodeType}) to scene: ${scenePath}`);

    // Execute the add_node operation
    const result = await this.operationExecutor.executeOperation(
      'add_node',
      {
        scenePath,
        nodeType,
        nodeName,
        parentNodePath,
        properties,
      },
      projectPath
    );

    if (result.success) {
      this.logger.info(`Node added successfully: ${nodeName}`);
    } else {
      this.logger.error(`Failed to add node: ${result.error}`);
    }

    return result;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    this.logger.error(`Failed to add node: ${errorMessage}`);
    
    return {
      success: false,
      error: errorMessage,
    };
  }
}

/**
 * Save changes to a scene file
 */
async function saveScene(this: { operationExecutor: GodotOperationExecutor; logger: any }, params: OperationParams): Promise<OperationResult> {
  try {
    const { projectPath, scenePath, newPath } = params;

    // Validate project path
    if (!ParameterValidator.validateProjectPath(projectPath)) {
      return {
        success: false,
        error: `Invalid project path: ${projectPath}`,
      };
    }

    // Validate scene path
    if (!ParameterValidator.validateScenePath(scenePath, projectPath)) {
      return {
        success: false,
        error: `Invalid scene path: ${scenePath}`,
      };
    }

    // Validate new path if provided
    if (newPath && !ParameterValidator.validateScenePath(newPath, projectPath)) {
      return {
        success: false,
        error: `Invalid new scene path: ${newPath}`,
      };
    }

    this.logger.info(`Saving scene: ${scenePath}${newPath ? ` to: ${newPath}` : ''}`);

    // Execute the save_scene operation
    const operationParams: any = { scenePath };
    if (newPath) {
      operationParams.newPath = newPath;
    }

    const result = await this.operationExecutor.executeOperation(
      'save_scene',
      operationParams,
      projectPath
    );

    if (result.success) {
      this.logger.info(`Scene saved successfully: ${newPath || scenePath}`);
    } else {
      this.logger.error(`Failed to save scene: ${result.error}`);
    }

    return result;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    this.logger.error(`Failed to save scene: ${errorMessage}`);
    
    return {
      success: false,
      error: errorMessage,
    };
  }
}

/**
 * Load a sprite into a Sprite2D node
 */
async function loadSprite(this: { operationExecutor: GodotOperationExecutor; logger: any }, params: OperationParams): Promise<OperationResult> {
  try {
    const { projectPath, scenePath, nodePath, texturePath } = params;

    // Validate project path
    if (!ParameterValidator.validateProjectPath(projectPath)) {
      return {
        success: false,
        error: `Invalid project path: ${projectPath}`,
      };
    }

    // Validate scene path
    if (!ParameterValidator.validateScenePath(scenePath, projectPath)) {
      return {
        success: false,
        error: `Invalid scene path: ${scenePath}`,
      };
    }

    // Validate texture path
    if (!ParameterValidator.validateResourcePath(texturePath, projectPath)) {
      return {
        success: false,
        error: `Invalid texture path: ${texturePath}`,
      };
    }

    // Validate node path
    if (!nodePath || typeof nodePath !== 'string') {
      return {
        success: false,
        error: 'Node path is required',
      };
    }

    this.logger.info(`Loading sprite ${texturePath} into node ${nodePath} in scene: ${scenePath}`);

    // Execute the load_sprite operation
    const result = await this.operationExecutor.executeOperation(
      'load_sprite',
      {
        scenePath,
        nodePath,
        texturePath,
      },
      projectPath
    );

    if (result.success) {
      this.logger.info(`Sprite loaded successfully: ${texturePath}`);
    } else {
      this.logger.error(`Failed to load sprite: ${result.error}`);
    }

    return result;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    this.logger.error(`Failed to load sprite: ${errorMessage}`);
    
    return {
      success: false,
      error: errorMessage,
    };
  }
}

/**
 * Scene tools definition
 */
export const sceneTools: MCPTool[] = [
  {
    name: 'create_scene',
    description: 'Create a new Godot scene file',
    inputSchema: {
      type: 'object',
      properties: {
        projectPath: {
          type: 'string',
          description: 'Path to the Godot project directory',
        },
        scenePath: {
          type: 'string',
          description: 'Path where the scene file will be saved (relative to project)',
        },
        rootNodeType: {
          type: 'string',
          description: 'Type of the root node (e.g., Node2D, Node3D)',
          default: 'Node2D',
        },
      },
      required: ['projectPath', 'scenePath'],
    },
    handler: createScene,
  },
  {
    name: 'add_node',
    description: 'Add a node to an existing scene',
    inputSchema: {
      type: 'object',
      properties: {
        projectPath: {
          type: 'string',
          description: 'Path to the Godot project directory',
        },
        scenePath: {
          type: 'string',
          description: 'Path to the scene file (relative to project)',
        },
        nodeType: {
          type: 'string',
          description: 'Type of node to add (e.g., Sprite2D, CollisionShape2D)',
        },
        nodeName: {
          type: 'string',
          description: 'Name for the new node',
        },
        parentNodePath: {
          type: 'string',
          description: 'Path to the parent node (e.g., "root" or "root/Player")',
          default: 'root',
        },
        properties: {
          type: 'object',
          description: 'Optional properties to set on the node',
        },
      },
      required: ['projectPath', 'scenePath', 'nodeType', 'nodeName'],
    },
    handler: addNode,
  },
  {
    name: 'load_sprite',
    description: 'Load a sprite into a Sprite2D node',
    inputSchema: {
      type: 'object',
      properties: {
        projectPath: {
          type: 'string',
          description: 'Path to the Godot project directory',
        },
        scenePath: {
          type: 'string',
          description: 'Path to the scene file (relative to project)',
        },
        nodePath: {
          type: 'string',
          description: 'Path to the Sprite2D node (e.g., "root/Player/Sprite2D")',
        },
        texturePath: {
          type: 'string',
          description: 'Path to the texture file (relative to project)',
        },
      },
      required: ['projectPath', 'scenePath', 'nodePath', 'texturePath'],
    },
    handler: loadSprite,
  },
  {
    name: 'save_scene',
    description: 'Save changes to a scene file',
    inputSchema: {
      type: 'object',
      properties: {
        projectPath: {
          type: 'string',
          description: 'Path to the Godot project directory',
        },
        scenePath: {
          type: 'string',
          description: 'Path to the scene file (relative to project)',
        },
        newPath: {
          type: 'string',
          description: 'Optional: New path to save the scene to (for creating variants)',
        },
      },
      required: ['projectPath', 'scenePath'],
    },
    handler: saveScene,
  },
];

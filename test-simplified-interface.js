#!/usr/bin/env node
/**
 * 测试简化工具接口
 */

import { GodotMCPServer } from './build/core/server.js';
import { AIConfigManager } from './build/ai/ai-config.js';

async function testSimplifiedInterface() {
  try {
    console.log('🔧 测试简化工具接口...\n');
    
    // 1. 测试传统接口
    console.log('1. 创建传统接口服务器...');
    const traditionalServer = new GodotMCPServer({ useSimplifiedInterface: false });
    await traditionalServer.start();
    const traditionalStats = traditionalServer.getServerStats();

    console.log('传统接口统计:');
    console.log(`- 工具总数: ${traditionalStats.toolRegistry.totalTools}`);
    console.log(`- 接口类型: 传统接口 (14个基础工具 + 4个智能工具)`);
    await traditionalServer.stop();

    // 2. 测试简化接口
    console.log('\n2. 创建简化接口服务器...');
    const simplifiedServer = new GodotMCPServer({ useSimplifiedInterface: true });
    await simplifiedServer.start();
    const simplifiedStats = simplifiedServer.getServerStats();

    console.log('简化接口统计:');
    console.log(`- 工具总数: ${simplifiedStats.toolRegistry.totalTools}`);
    console.log(`- 接口类型: 简化接口 (3个高级智能工具)`);
    await simplifiedServer.stop();
    
    // 3. 检查AI配置
    console.log('\n3. 检查AI配置状态...');
    const aiConfig = AIConfigManager.getInstance();
    const configSummary = aiConfig.getConfigSummary();
    
    console.log(`AI功能状态: ${aiConfig.isAIEnabled() ? '✅ 已启用' : '❌ 未启用'}`);
    console.log(`模型供应商: ${configSummary.model.provider}`);
    console.log(`模型名称: ${configSummary.model.model}`);
    
    // 4. 显示工具对比
    console.log('\n4. 工具接口对比:');
    console.log('\n传统接口 (18个工具):');
    console.log('编辑器工具:');
    console.log('  - launch_editor: 启动Godot编辑器');
    console.log('  - run_project: 运行项目');
    console.log('  - get_debug_output: 获取调试输出');
    console.log('  - stop_project: 停止项目');
    console.log('  - get_godot_version: 获取Godot版本');
    
    console.log('项目工具:');
    console.log('  - list_projects: 列出项目');
    console.log('  - get_project_info: 获取项目信息');
    
    console.log('场景工具:');
    console.log('  - create_scene: 创建场景');
    console.log('  - add_node: 添加节点');
    console.log('  - load_sprite: 加载精灵');
    console.log('  - save_scene: 保存场景');
    
    console.log('资源工具:');
    console.log('  - export_mesh_library: 导出网格库');
    console.log('  - get_uid: 获取UID');
    console.log('  - update_project_uids: 更新项目UID');
    
    console.log('智能工具:');
    console.log('  - intelligent_scene_operation: 智能场景操作');
    console.log('  - query_project_state: 查询项目状态');
    console.log('  - manage_project: 管理项目');
    console.log('  - debug_and_run: 调试运行');
    
    console.log('\n简化接口 (3个工具):');
    console.log('  - godot_operation: 统一的Godot操作工具');
    console.log('    * 替代所有编辑器、场景、资源工具');
    console.log('    * 使用AI理解自然语言描述');
    console.log('    * 自动选择合适的底层操作');
    
    console.log('  - project_query: 项目查询和分析工具');
    console.log('    * 替代所有查询相关工具');
    console.log('    * 智能分析项目状态');
    console.log('    * 提供详细的项目信息');
    
    console.log('  - project_management: 项目管理工具');
    console.log('    * 替代所有管理配置工具');
    console.log('    * 执行高级项目操作');
    console.log('    * 优化和配置项目');
    
    // 5. 显示使用示例
    console.log('\n5. 使用示例:');
    
    console.log('\n传统接口使用:');
    console.log('```json');
    console.log('{');
    console.log('  "tool": "create_scene",');
    console.log('  "params": {');
    console.log('    "projectPath": "/path/to/project",');
    console.log('    "scenePath": "scenes/player.tscn",');
    console.log('    "rootNodeType": "CharacterBody2D"');
    console.log('  }');
    console.log('}');
    console.log('```');
    
    console.log('\n简化接口使用:');
    console.log('```json');
    console.log('{');
    console.log('  "tool": "godot_operation",');
    console.log('  "params": {');
    console.log('    "projectPath": "/path/to/project",');
    console.log('    "description": "创建一个2D平台游戏的主角色场景，包含精灵、碰撞体和移动脚本"');
    console.log('  }');
    console.log('}');
    console.log('```');
    
    // 6. 显示优势
    console.log('\n6. 简化接口优势:');
    console.log('✅ 工具数量减少: 18个 → 3个 (减少83%)');
    console.log('✅ 自然语言交互: 无需记忆复杂的参数结构');
    console.log('✅ AI智能规划: 自动分解复杂任务为具体操作');
    console.log('✅ 向后兼容: 支持传统工具的所有参数');
    console.log('✅ 错误恢复: AI可以理解错误并自动修正');
    console.log('✅ 上下文感知: 考虑项目状态和历史操作');
    
    console.log('\n🎉 简化接口测试完成!');
    
    if (!aiConfig.isAIEnabled()) {
      console.log('\n⚠️  注意: AI功能未启用，简化接口需要AI支持');
      console.log('请在.env文件中配置AI模型参数以使用简化接口');
    } else {
      console.log('\n✅ AI功能已就绪，可以使用简化接口');
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    console.error('错误详情:', error.stack);
  }
}

// 运行测试
testSimplifiedInterface().catch(console.error);

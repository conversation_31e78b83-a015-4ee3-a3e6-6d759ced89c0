#!/usr/bin/env -S godot --headless --script
# GDScript Class Utilities Module
# Provides utilities for class instantiation and script loading

class_name GodotClassUtils

# Get script by class name or path
static func get_script_by_name(name_of_class: String) -> Script:
	GodotLogger.log_debug("Attempting to get script for class: " + name_of_class)
	
	# Try to load it directly if it's a resource path
	if ResourceLoader.exists(name_of_class, "Script"):
		GodotLogger.log_debug("Resource exists, loading directly: " + name_of_class)
		var script = load(name_of_class) as Script
		if script:
			GodotLogger.log_debug("Successfully loaded script from path")
			return script
		else:
			GodotLogger.log_error("Failed to load script from path: " + name_of_class)
	else:
		GodotLogger.log_debug("Resource not found, checking global class registry")
	
	# Search for it in the global class registry if it's a class name
	var global_classes = ProjectSettings.get_global_class_list()
	GodotLogger.log_debug("Searching through " + str(global_classes.size()) + " global classes")
	
	for global_class in global_classes:
		var found_name_of_class = global_class["class"]
		var found_path = global_class["path"]
		
		if found_name_of_class == name_of_class:
			GodotLogger.log_debug("Found matching class in registry: " + found_name_of_class + " at path: " + found_path)
			var script = load(found_path) as Script
			if script:
				GodotLogger.log_debug("Successfully loaded script from registry")
				return script
			else:
				GodotLogger.log_error("Failed to load script from registry path: " + found_path)
				break
	
	GodotLogger.log_error("Could not find script for class: " + name_of_class)
	return null

# Instantiate a class by name
static func instantiate_class(name_of_class: String) -> Object:
	if name_of_class.is_empty():
		GodotLogger.log_error("Cannot instantiate class: name is empty")
		return null
	
	var result = null
	
	GodotLogger.log_debug("Attempting to instantiate class: " + name_of_class)
	
	# First, try to get it as a built-in class
	if ClassDB.class_exists(name_of_class):
		GodotLogger.log_debug("Found built-in class: " + name_of_class)
		result = ClassDB.instantiate(name_of_class)
		if result:
			GodotLogger.log_debug("Successfully instantiated built-in class")
			return result
		else:
			GodotLogger.log_error("Failed to instantiate built-in class: " + name_of_class)
	else:
		GodotLogger.log_debug("Not a built-in class, trying script-based class")
	
	# If not a built-in class, try to get the script and instantiate it
	var script = get_script_by_name(name_of_class)
	if script:
		GodotLogger.log_debug("Found script for class, attempting to instantiate")
		result = script.new()
		if result:
			GodotLogger.log_debug("Successfully instantiated script-based class")
			return result
		else:
			GodotLogger.log_error("Failed to instantiate script-based class")
	
	GodotLogger.log_error("Could not instantiate class: " + name_of_class)
	return null

# Check if a class exists (built-in or script-based)
static func class_exists(name_of_class: String) -> bool:
	if ClassDB.class_exists(name_of_class):
		return true
	
	var script = get_script_by_name(name_of_class)
	return script != null

# Get class information for debugging
static func get_class_info(name_of_class: String) -> Dictionary:
	var info = {
		"class_name": name_of_class,
		"is_builtin": ClassDB.class_exists(name_of_class),
		"script_found": false,
		"script_path": "",
		"can_instantiate": false
	}
	
	if info.is_builtin:
		info.can_instantiate = true
	else:
		var script = get_script_by_name(name_of_class)
		if script:
			info.script_found = true
			info.script_path = script.resource_path
			info.can_instantiate = true
	
	return info

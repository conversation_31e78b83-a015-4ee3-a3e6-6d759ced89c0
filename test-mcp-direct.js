#!/usr/bin/env node

/**
 * 直接测试MCP调用 - 模拟实际的MCP客户端调用
 */

import dotenv from 'dotenv';
import { GodotMCPServer } from './build/core/server.js';

// 加载环境变量
dotenv.config();

// 启用简化接口
process.env.USE_SIMPLIFIED_INTERFACE = 'true';

async function testDirectMCPCall() {
  console.log('🔧 测试直接MCP调用...\n');

  let server = null;
  
  try {
    // 1. 启动MCP服务器
    console.log('1. 启动MCP服务器...');
    server = new GodotMCPServer();
    await server.start();
    console.log('   ✅ MCP服务器启动成功\n');

    // 2. 获取工具列表
    console.log('2. 获取可用工具...');
    const toolRegistry = server.getToolRegistry();
    const toolNames = Array.from(toolRegistry.tools.keys());
    console.log(`   📋 可用工具 (${toolNames.length}个):`);
    toolNames.forEach((name, index) => {
      console.log(`      ${index + 1}. ${name}`);
    });
    console.log('');

    // 3. 测试godot_operation工具
    console.log('3. 测试godot_operation工具...');
    const testParams = {
      projectPath: "E:\\Github\\4.2.1\\Game-6",
      description: "分析场景文件 res://Scenes/Game/Island/IslandBase.tscn 的内容",
      operation: "get_scene_content",
      scenePath: "res://Scenes/Game/Island/IslandBase.tscn"
    };

    console.log('   📝 测试参数:');
    console.log(`      项目路径: ${testParams.projectPath}`);
    console.log(`      描述: ${testParams.description}`);
    console.log(`      操作: ${testParams.operation}`);
    console.log(`      场景路径: ${testParams.scenePath}`);
    console.log('');

    console.log('   ⏳ 执行MCP调用...');
    const startTime = Date.now();

    // 直接调用工具注册表
    const result = await toolRegistry.callTool('godot_operation', testParams);
    
    const duration = Date.now() - startTime;
    console.log(`\n   ✅ MCP调用完成! (耗时: ${duration}ms)`);

    // 4. 分析结果
    console.log('\n4. 结果分析:');
    if (result.success) {
      console.log('   ✅ 操作成功');
      console.log(`      消息: ${result.message || '无消息'}`);
      if (result.data) {
        console.log(`      数据: ${JSON.stringify(result.data, null, 2)}`);
      }
    } else {
      console.log('   ❌ 操作失败');
      console.log(`      错误: ${result.error || '未知错误'}`);
    }

  } catch (error) {
    console.error('\n❌ 测试失败:', error.message);
    console.error('   详细错误:', error);
  } finally {
    // 5. 清理
    if (server) {
      console.log('\n5. 停止MCP服务器...');
      await server.stop();
      console.log('   ✅ MCP服务器已停止');
    }
  }

  console.log('\n🏁 直接MCP调用测试完成!\n');
}

// 运行测试
testDirectMCPCall().catch(console.error);

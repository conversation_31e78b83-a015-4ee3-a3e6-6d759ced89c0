/**
 * 持久化Godot代理管理器
 * 管理与长时间运行的Godot进程的通信
 */

import { spawn, ChildProcess } from 'child_process';
import { EventEmitter } from 'events';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';
import { writeFileSync, readFileSync, existsSync, unlinkSync } from 'fs';
import { Logger } from '../utils/logger.js';
import { GodotPathDetector } from '../godot/path-detector.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const logger = Logger.createScopedLogger('PersistentGodotManager');

export interface GodotCommand {
  action: string;
  [key: string]: any;
}

export interface GodotResponse {
  type: string;
  message?: string;
  data?: any;
  error?: string;
  timestamp?: number;
}

/**
 * 持久化Godot代理管理器
 */
export class PersistentGodotManager extends EventEmitter {
  private godotProcess: ChildProcess | null = null;
  private pathDetector: GodotPathDetector;
  private agentScriptPath: string;
  private isRunning: boolean = false;
  private commandQueue: Array<{
    command: GodotCommand;
    resolve: (response: GodotResponse) => void;
    reject: (error: Error) => void;
  }> = [];
  private commandFilePath: string;
  private responseFilePath: string;
  private responseWatcher: NodeJS.Timeout | null = null;

  constructor(pathDetector: GodotPathDetector) {
    super();
    this.pathDetector = pathDetector;
    this.agentScriptPath = join(__dirname, '..', '..', 'scripts', 'godot_persistent_agent_v2.gd');
    this.commandFilePath = join(process.cwd(), 'godot_agent_commands.json');
    this.responseFilePath = join(process.cwd(), 'godot_agent_responses.json');
    logger.debug(`Agent script path: ${this.agentScriptPath}`);
    logger.debug(`Command file path: ${this.commandFilePath}`);
    logger.debug(`Response file path: ${this.responseFilePath}`);
  }

  /**
   * 启动持久化Godot代理
   */
  public async startAgent(projectPath: string): Promise<void> {
    if (this.isRunning) {
      logger.warn('Godot agent is already running');
      return;
    }

    try {
      logger.info(`Starting persistent Godot agent for project: ${projectPath}`);

      // 获取Godot路径
      const godotPath = await this.pathDetector.detectGodotPath();
      if (!godotPath) {
        throw new Error('Could not find Godot executable. Please set GODOT_PATH environment variable.');
      }
      logger.info(`Using Godot executable: ${godotPath}`);

      // 验证项目路径
      const fs = await import('fs/promises');
      const path = await import('path');
      const projectFile = path.join(projectPath, 'project.godot');
      try {
        await fs.access(projectFile);
        logger.info(`Project file found: ${projectFile}`);
      } catch {
        throw new Error(`Invalid project path: ${projectPath}. project.godot not found.`);
      }

      // 清理旧的通信文件
      this.cleanupFiles();
      logger.info('Cleaned up old communication files');

      // 启动Godot进程
      const args = [
        '--headless',
        '--path',
        projectPath,
        '--script',
        this.agentScriptPath,
        '--command-file',
        this.commandFilePath,
        '--response-file',
        this.responseFilePath
      ];

      logger.info(`Starting Godot with args: ${args.join(' ')}`);
      logger.info(`Command file: ${this.commandFilePath}`);
      logger.info(`Response file: ${this.responseFilePath}`);
      logger.info(`Agent script: ${this.agentScriptPath}`);

      this.godotProcess = spawn(godotPath, args, {
        stdio: ['pipe', 'pipe', 'pipe']
      });

      logger.info(`Godot process started with PID: ${this.godotProcess.pid}`);

      // 设置事件监听器
      this.setupProcessEventListeners();

      // 等待代理启动
      await this.waitForAgentStart();

      this.isRunning = true;
      logger.info('Persistent Godot agent started successfully');

    } catch (error) {
      logger.error('Failed to start Godot agent:', error);
      throw error;
    }
  }

  /**
   * 停止持久化Godot代理
   */
  public async stopAgent(): Promise<void> {
    if (!this.isRunning || !this.godotProcess) {
      return;
    }

    try {
      logger.info('Stopping persistent Godot agent...');

      // 发送关闭命令
      await this.sendCommand({ action: 'shutdown' });

      // 等待进程结束
      await new Promise<void>((resolve) => {
        if (this.godotProcess) {
          this.godotProcess.on('exit', () => resolve());
          // 如果5秒后还没结束，强制杀死
          setTimeout(() => {
            if (this.godotProcess && !this.godotProcess.killed) {
              this.godotProcess.kill('SIGKILL');
              resolve();
            }
          }, 5000);
        } else {
          resolve();
        }
      });

    } catch (error) {
      logger.error('Error stopping Godot agent:', error);
    } finally {
      this.cleanup();
    }
  }

  /**
   * 发送命令到Godot代理
   */
  public async sendCommand(command: GodotCommand): Promise<GodotResponse> {
    if (!this.isRunning || !this.godotProcess) {
      throw new Error('Godot agent is not running');
    }

    return new Promise((resolve, reject) => {
      // 添加到命令队列
      this.commandQueue.push({ command, resolve, reject });

      // 写入命令文件
      try {
        const commandJson = JSON.stringify(command);
        writeFileSync(this.commandFilePath, commandJson, 'utf8');
        logger.debug(`Sent command: ${command.action}`);

        // 开始监听响应
        this.startResponseWatcher();
      } catch (error) {
        reject(new Error(`Failed to write command file: ${error}`));
      }
    });
  }

  /**
   * 检查代理是否运行
   */
  public isAgentRunning(): boolean {
    return this.isRunning && this.godotProcess !== null && !this.godotProcess.killed;
  }

  /**
   * 设置进程事件监听器
   */
  private setupProcessEventListeners(): void {
    if (!this.godotProcess) return;

    // 监听标准输出（用于调试信息）
    this.godotProcess.stdout!.on('data', (data: Buffer) => {
      logger.debug('Godot stdout:', data.toString());
    });

    // 监听标准错误
    this.godotProcess.stderr!.on('data', (data: Buffer) => {
      logger.warn('Godot stderr:', data.toString());
    });

    // 监听进程退出
    this.godotProcess.on('exit', (code, signal) => {
      logger.info(`Godot agent exited with code ${code}, signal ${signal}`);
      this.cleanup();
    });

    // 监听进程错误
    this.godotProcess.on('error', (error) => {
      logger.error('Godot agent process error:', error);
      this.cleanup();
    });
  }

  /**
   * 开始响应文件监听
   */
  private startResponseWatcher(): void {
    if (this.responseWatcher) {
      return; // 已经在监听
    }

    this.responseWatcher = setInterval(() => {
      if (existsSync(this.responseFilePath)) {
        this.handleResponseFile();
      }
    }, 100); // 每100ms检查一次
  }

  /**
   * 处理响应文件
   */
  private handleResponseFile(): void {
    try {
      const responseJson = readFileSync(this.responseFilePath, 'utf8');
      unlinkSync(this.responseFilePath); // 删除响应文件

      if (responseJson.trim()) {
        this.processResponse(responseJson);
      }
    } catch (error) {
      logger.error('Failed to read response file:', error);
    }
  }

  /**
   * 清理通信文件
   */
  private cleanupFiles(): void {
    try {
      if (existsSync(this.commandFilePath)) {
        unlinkSync(this.commandFilePath);
      }
      if (existsSync(this.responseFilePath)) {
        unlinkSync(this.responseFilePath);
      }
    } catch (error) {
      logger.warn('Failed to cleanup files:', error);
    }
  }



  /**
   * 处理响应
   */
  private processResponse(responseJson: string): void {
    try {
      const response: GodotResponse = JSON.parse(responseJson);
      logger.debug(`Received response: ${response.type}`);

      // 处理队列中的命令
      if (this.commandQueue.length > 0) {
        const { resolve } = this.commandQueue.shift()!;
        resolve(response);
      }

      // 发出事件
      this.emit('response', response);

    } catch (error) {
      logger.error('Failed to parse Godot response:', error);
      logger.error('Raw response:', responseJson);
    }
  }

  /**
   * 等待代理启动
   */
  private async waitForAgentStart(): Promise<void> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        logger.error('Godot代理启动超时，可能的原因：');
        logger.error('1. Godot路径配置错误');
        logger.error('2. 项目路径不存在或无效');
        logger.error('3. Godot进程启动失败');
        logger.error('4. 防火墙或权限问题');
        reject(new Error('Timeout waiting for Godot agent to start (30s). Check Godot path and project path.'));
      }, 30000); // 增加到30秒

      const onResponse = (response: GodotResponse) => {
        if (response.type === 'agent_started') {
          clearTimeout(timeout);
          this.off('response', onResponse);
          resolve();
        }
      };

      this.on('response', onResponse);
    });
  }

  /**
   * 清理资源
   */
  private cleanup(): void {
    this.isRunning = false;
    this.godotProcess = null;

    // 停止响应监听
    if (this.responseWatcher) {
      clearInterval(this.responseWatcher);
      this.responseWatcher = null;
    }

    // 清理通信文件
    this.cleanupFiles();

    // 拒绝所有待处理的命令
    for (const { reject } of this.commandQueue) {
      reject(new Error('Godot agent stopped'));
    }
    this.commandQueue = [];

    this.emit('stopped');
  }
}

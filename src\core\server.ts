/**
 * Core MCP server implementation for Godot MCP
 */

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { MCPServerConfig, MCP_SERVER_INFO } from './types.js';
import { ConfigManager } from './config.js';
import { Logger } from '../utils/logger.js';
import { GodotPathDetector } from '../godot/path-detector.js';
import { GodotProcessManager } from '../godot/process-manager.js';
import { GodotOperationExecutor } from '../godot/operation-executor.js';
import { IntelligentAgentController } from '../agent/intelligent-agent-controller.js';
import { ToolRegistry } from '../tools/index.js';

const logger = Logger.createScopedLogger('Server');

/**
 * Main Godot MCP server class
 */
export class GodotMCPServer {
  private server: Server;
  private configManager: ConfigManager;
  private pathDetector: GodotPathDetector;
  private processManager: GodotProcessManager;
  private operationExecutor: GodotOperationExecutor;
  private agentController: IntelligentAgentController;
  private toolRegistry: ToolRegistry;
  private isRunning: boolean = false;

  constructor(config?: Partial<MCPServerConfig>) {
    // Initialize configuration
    this.configManager = ConfigManager.getInstance();
    
    // Initialize core components
    this.pathDetector = new GodotPathDetector();
    this.processManager = new GodotProcessManager();
    this.operationExecutor = new GodotOperationExecutor(this.pathDetector, this.processManager);
    this.agentController = new IntelligentAgentController(this.pathDetector);

    // Create MCP server instance
    const serverConfig: MCPServerConfig = {
      name: MCP_SERVER_INFO.name,
      version: MCP_SERVER_INFO.version,
      capabilities: {
        tools: {},
      },
      ...config,
    };

    this.server = new Server(serverConfig, {
      capabilities: serverConfig.capabilities,
    });

    // Initialize tool registry
    this.toolRegistry = new ToolRegistry(this.server, this.operationExecutor, this.agentController);

    // Set up error handling
    this.setupErrorHandling();

    // Set up cleanup handlers
    this.setupCleanupHandlers();

    logger.debug('Godot MCP Server initialized');
  }

  /**
   * Start the MCP server
   */
  public async start(): Promise<void> {
    try {
      logger.info('Starting Godot MCP Server...');

      // Detect Godot path before starting
      await this.initializeGodotPath();

      // Validate operations script
      if (!this.operationExecutor.validateOperationsScript()) {
        throw new Error('Godot operations script not found');
      }

      // Register all tools
      this.toolRegistry.registerAllTools();

      // Log server configuration
      this.logServerConfiguration();

      // Connect to stdio transport
      const transport = new StdioServerTransport();
      await this.server.connect(transport);

      this.isRunning = true;
      logger.info('Godot MCP Server started successfully');

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logger.error(`Failed to start server: ${errorMessage}`);
      throw error;
    }
  }

  /**
   * Stop the MCP server
   */
  public async stop(): Promise<void> {
    try {
      logger.info('Stopping Godot MCP Server...');

      this.isRunning = false;

      // Clean up processes
      await this.processManager.cleanup();

      // Clear path cache
      this.pathDetector.clearCache();

      logger.info('Godot MCP Server stopped successfully');

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logger.error(`Error stopping server: ${errorMessage}`);
      throw error;
    }
  }

  /**
   * Check if the server is running
   */
  public isServerRunning(): boolean {
    return this.isRunning;
  }

  /**
   * Get server statistics
   */
  public getServerStats(): {
    isRunning: boolean;
    config: Record<string, any>;
    pathDetector: any;
    processManager: any;
    operationExecutor: any;
    toolRegistry: any;
  } {
    return {
      isRunning: this.isRunning,
      config: this.configManager.exportConfig(),
      pathDetector: this.pathDetector.getCacheStats(),
      processManager: this.processManager.getProcessStats(),
      operationExecutor: this.operationExecutor.getExecutorStats(),
      toolRegistry: this.toolRegistry.getRegistryStats(),
    };
  }

  /**
   * Initialize Godot path detection
   */
  private async initializeGodotPath(): Promise<void> {
    logger.info('Detecting Godot executable path...');

    const godotPath = await this.pathDetector.detectGodotPath();
    
    if (!godotPath) {
      throw new Error(
        'Failed to find a valid Godot executable path. ' +
        'Please set GODOT_PATH environment variable or ensure Godot is installed.'
      );
    }

    // Validate the detected path
    const isValid = await this.pathDetector.isValidGodotPath(godotPath);
    if (!isValid) {
      throw new Error(`Detected Godot path is not valid: ${godotPath}`);
    }

    logger.info(`Using Godot at: ${godotPath}`);
  }

  /**
   * Set up error handling
   */
  private setupErrorHandling(): void {
    this.server.onerror = (error) => {
      logger.error('MCP Server error:', error);
    };

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught exception:', error);
      this.stop().finally(() => {
        process.exit(1);
      });
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled promise rejection:', reason);
      logger.debug('Promise:', promise);
    });
  }

  /**
   * Set up cleanup handlers
   */
  private setupCleanupHandlers(): void {
    // Handle SIGINT (Ctrl+C)
    process.on('SIGINT', async () => {
      logger.info('Received SIGINT, shutting down gracefully...');
      await this.stop();
      process.exit(0);
    });

    // Handle SIGTERM
    process.on('SIGTERM', async () => {
      logger.info('Received SIGTERM, shutting down gracefully...');
      await this.stop();
      process.exit(0);
    });

    // Handle process exit
    process.on('exit', (code) => {
      logger.info(`Process exiting with code: ${code}`);
    });
  }

  /**
   * Log server configuration
   */
  private logServerConfiguration(): void {
    const stats = this.getServerStats();
    
    logger.info('='.repeat(50));
    logger.info('Godot MCP Server Configuration');
    logger.info('='.repeat(50));
    logger.info(`Server Name: ${MCP_SERVER_INFO.name}`);
    logger.info(`Server Version: ${MCP_SERVER_INFO.version}`);
    logger.info(`Debug Mode: ${stats.config.debugMode}`);
    logger.info(`Godot Debug Mode: ${stats.config.godotDebugMode}`);
    logger.info(`Strict Path Validation: ${stats.config.strictPathValidation}`);
    logger.info(`Godot Path: ${stats.config.godotPath}`);
    logger.info(`Operations Script: ${stats.operationExecutor.operationsScriptPath}`);
    logger.info(`Registered Tools: ${stats.toolRegistry.totalTools}`);
    logger.info('='.repeat(50));
  }

  /**
   * Get the underlying MCP server instance (for advanced usage)
   */
  public getMCPServer(): Server {
    return this.server;
  }

  /**
   * Get the tool registry (for advanced usage)
   */
  public getToolRegistry(): ToolRegistry {
    return this.toolRegistry;
  }

  /**
   * Get the operation executor (for advanced usage)
   */
  public getOperationExecutor(): GodotOperationExecutor {
    return this.operationExecutor;
  }
}

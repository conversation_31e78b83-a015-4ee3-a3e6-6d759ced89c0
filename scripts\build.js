import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Make the build/index.js file executable
fs.chmodSync(path.join(__dirname, '..', 'build', 'index.js'), '755');

// Copy the scripts directory to the build directory
try {
  // Ensure the build/scripts directory exists
  fs.ensureDirSync(path.join(__dirname, '..', 'build', 'scripts'));
  
  // Copy the godot_operations.gd file
  fs.copyFileSync(
    path.join(__dirname, '..', 'src', 'scripts', 'godot_operations.gd'),
    path.join(__dirname, '..', 'build', 'scripts', 'godot_operations.gd')
  );

  // Copy the godot_operations_helpers.gd file
  fs.copyFileSync(
    path.join(__dirname, '..', 'src', 'scripts', 'godot_operations_helpers.gd'),
    path.join(__dirname, '..', 'build', 'scripts', 'godot_operations_helpers.gd')
  );

  // Update the helper file path in the build version
  const buildMainFile = path.join(__dirname, '..', 'build', 'scripts', 'godot_operations.gd');
  let content = fs.readFileSync(buildMainFile, 'utf8');
  console.log('Original helper path found:', content.includes('res://src/scripts/godot_operations_helpers.gd'));
  content = content.replace(
    'res://src/scripts/godot_operations_helpers.gd',
    'res://build/scripts/godot_operations_helpers.gd'
  );
  fs.writeFileSync(buildMainFile, content);
  console.log('Updated helper path in build version');

  console.log('Successfully copied godot_operations.gd and helpers to build/scripts');
} catch (error) {
  console.error('Error copying scripts:', error);
  process.exit(1);
}

console.log('Build scripts completed successfully!');

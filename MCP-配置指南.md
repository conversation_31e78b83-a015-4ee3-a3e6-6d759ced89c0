# Godot MCP 配置指南

## 🎯 **问题解答**

### **1. MCP服务配置**

您的原始配置基本正确，但需要添加关键的环境变量：

#### ✅ **推荐配置 (简化接口 - 3个工具)**
```json
{
  "mcpServers": {
    "godot-mcp": {
      "command": "node",
      "args": ["D:/MCP/test-mcp/godot-mcp-1/build/index.js"],
      "env": {
        "USE_SIMPLIFIED_INTERFACE": "true",
        "GODOT_PATH": "D:\\Godot_v4.2.1-stable_mono_win64\\godot.exe",
        "DEBUG": "false",
        "READ_ONLY_MODE": "false"
      }
    }
  }
}
```

#### 📋 **传统接口配置 (18个工具)**
```json
{
  "mcpServers": {
    "godot-mcp-traditional": {
      "command": "node",
      "args": ["D:/MCP/test-mcp/godot-mcp-1/build/index.js"],
      "env": {
        "USE_SIMPLIFIED_INTERFACE": "false",
        "GODOT_PATH": "D:\\Godot_v4.2.1-stable_mono_win64\\godot.exe",
        "DEBUG": "false",
        "READ_ONLY_MODE": "false"
      }
    }
  }
}
```

### **2. 工具数量问题解决**

✅ **已修复！** 现在系统正确支持两种模式：

- **简化接口**: 3个高级智能工具
  - `godot_operation`: 统一的Godot操作工具
  - `project_query`: 项目查询和分析工具  
  - `project_management`: 项目管理工具

- **传统接口**: 18个基础工具 (14个基础 + 4个智能)

### **3. 智能代理启动超时问题修复**

✅ **已优化！** 修复内容：

1. **超时时间延长**: 从10秒增加到30秒
2. **详细错误日志**: 提供具体的故障排除信息
3. **启动验证**: 增加项目路径和Godot路径验证
4. **进程监控**: 添加详细的进程启动日志

## 🔧 **关键环境变量说明**

| 变量名 | 作用 | 推荐值 |
|--------|------|--------|
| `USE_SIMPLIFIED_INTERFACE` | 启用简化接口 | `"true"` (推荐) |
| `GODOT_PATH` | Godot可执行文件路径 | 您的Godot安装路径 |
| `DEBUG` | 调试模式 | `"false"` |
| `READ_ONLY_MODE` | 只读模式 | `"false"` |

## 🚀 **使用简化接口的优势**

### **传统接口 vs 简化接口**

| 特性 | 传统接口 | 简化接口 |
|------|----------|----------|
| 工具数量 | 18个 | 3个 |
| 学习成本 | 高 (需记忆复杂参数) | 低 (自然语言) |
| 操作方式 | 参数化调用 | AI理解描述 |
| 错误处理 | 手动处理 | AI自动修正 |
| 任务规划 | 手动分解 | AI智能规划 |

### **简化接口使用示例**

#### 传统方式 (复杂)：
```json
{
  "tool": "create_scene",
  "params": {
    "projectPath": "E:/Github/4.2.1/Game-6",
    "scenePath": "res://Scenes/Player.tscn",
    "rootNodeType": "CharacterBody2D"
  }
}
```

#### 简化方式 (自然语言)：
```json
{
  "tool": "godot_operation",
  "params": {
    "projectPath": "E:/Github/4.2.1/Game-6",
    "description": "创建一个2D平台游戏的主角色场景，包含精灵、碰撞体和移动脚本"
  }
}
```

## 🛠️ **故障排除指南**

### **智能代理启动失败**

如果遇到 `"Timeout waiting for Godot agent to start"` 错误：

1. ✅ **检查GODOT_PATH**: 确保路径正确且可执行
2. ✅ **验证项目路径**: 确保包含 `project.godot` 文件
3. ✅ **权限检查**: 确保Godot有执行权限
4. ✅ **防火墙设置**: 检查是否被防火墙阻止
5. ✅ **耐心等待**: 超时时间已增加到30秒
6. ✅ **查看日志**: 启用DEBUG模式获取详细信息

### **AI功能配置**

要使用简化接口的AI功能，需要在 `.env` 文件中配置：

```env
OPENAI_API_KEY=your_api_key_here
OPENAI_BASE_URL=https://api.mistral.ai/v1
OPENAI_MODEL=mistral-large-latest
```

## 📊 **测试验证**

运行以下命令验证配置：

```bash
# 编译项目
npm run build

# 测试配置
node test-mcp-configuration.js

# 测试AI集成
node test-ai-integration.js
```

## 🎉 **总结**

现在您的系统已经完全修复并优化：

- ✅ **简化接口**: 3个智能工具替代18个传统工具
- ✅ **环境变量支持**: 通过配置控制接口模式
- ✅ **智能代理优化**: 更长超时时间和详细错误信息
- ✅ **AI增强功能**: 自然语言操作和智能任务规划
- ✅ **向后兼容**: 支持传统接口的所有功能

**推荐使用简化接口配置以获得最佳的AI增强体验！** 🚀

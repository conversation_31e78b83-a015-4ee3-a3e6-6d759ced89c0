/**
 * Logging utility for Godot MCP Server
 */

import { LogLevel } from '../core/types.js';
import { ConfigManager } from '../core/config.js';

/**
 * Logger class for centralized logging
 */
export class Logger {
  private static configManager = ConfigManager.getInstance();

  /**
   * Log a debug message (only if debug mode is enabled)
   */
  public static debug(message: string, ...args: any[]): void {
    if (Logger.configManager.isDebugMode()) {
      Logger.log(LogLevel.DEBUG, message, ...args);
    }
  }

  /**
   * Log an info message
   */
  public static info(message: string, ...args: any[]): void {
    Logger.log(LogLevel.INFO, message, ...args);
  }

  /**
   * Log a warning message
   */
  public static warn(message: string, ...args: any[]): void {
    Logger.log(LogLevel.WARN, message, ...args);
  }

  /**
   * Log an error message
   */
  public static error(message: string, ...args: any[]): void {
    Logger.log(LogLevel.ERROR, message, ...args);
  }

  /**
   * Internal logging method
   */
  private static log(level: LogLevel, message: string, ...args: any[]): void {
    const timestamp = new Date().toISOString();
    const prefix = `[${timestamp}] [${level}]`;
    
    switch (level) {
      case LogLevel.DEBUG:
        console.debug(`${prefix} ${message}`, ...args);
        break;
      case LogLevel.INFO:
        console.log(`${prefix} ${message}`, ...args);
        break;
      case LogLevel.WARN:
        console.warn(`${prefix} ${message}`, ...args);
        break;
      case LogLevel.ERROR:
        console.error(`${prefix} ${message}`, ...args);
        break;
    }
  }

  /**
   * Log server startup information
   */
  public static logServerStart(godotPath: string): void {
    Logger.info('='.repeat(50));
    Logger.info('Godot MCP Server Starting');
    Logger.info('='.repeat(50));
    Logger.info(`Godot Path: ${godotPath}`);
    Logger.info(`Debug Mode: ${Logger.configManager.isDebugMode()}`);
    Logger.info(`Godot Debug Mode: ${Logger.configManager.isGodotDebugMode()}`);
    Logger.info(`Strict Path Validation: ${Logger.configManager.isStrictPathValidation()}`);
    Logger.info('='.repeat(50));
  }

  /**
   * Log operation execution
   */
  public static logOperation(operation: string, projectPath: string, params?: any): void {
    Logger.info(`Executing operation: ${operation}`);
    Logger.debug(`Project path: ${projectPath}`);
    if (params && Logger.configManager.isDebugMode()) {
      Logger.debug(`Parameters: ${JSON.stringify(params, null, 2)}`);
    }
  }

  /**
   * Log operation result
   */
  public static logOperationResult(operation: string, success: boolean, error?: string): void {
    if (success) {
      Logger.info(`Operation ${operation} completed successfully`);
    } else {
      Logger.error(`Operation ${operation} failed: ${error || 'Unknown error'}`);
    }
  }

  /**
   * Log process information
   */
  public static logProcess(action: string, command?: string): void {
    Logger.debug(`Process ${action}${command ? `: ${command}` : ''}`);
  }

  /**
   * Log path validation
   */
  public static logPathValidation(path: string, isValid: boolean, cached: boolean = false): void {
    const cacheInfo = cached ? ' (cached)' : '';
    Logger.debug(`Path validation${cacheInfo}: ${path} -> ${isValid ? 'VALID' : 'INVALID'}`);
  }

  /**
   * Log configuration changes
   */
  public static logConfigUpdate(updates: Record<string, any>): void {
    Logger.debug('Configuration updated:', updates);
  }

  /**
   * Log error with stack trace in debug mode
   */
  public static logErrorWithStack(message: string, error: Error): void {
    Logger.error(message);
    if (Logger.configManager.isDebugMode() && error.stack) {
      Logger.debug('Stack trace:', error.stack);
    }
  }

  /**
   * Create a scoped logger for a specific component
   */
  public static createScopedLogger(scope: string) {
    return {
      debug: (message: string, ...args: any[]) => Logger.debug(`[${scope}] ${message}`, ...args),
      info: (message: string, ...args: any[]) => Logger.info(`[${scope}] ${message}`, ...args),
      warn: (message: string, ...args: any[]) => Logger.warn(`[${scope}] ${message}`, ...args),
      error: (message: string, ...args: any[]) => Logger.error(`[${scope}] ${message}`, ...args),
    };
  }
}

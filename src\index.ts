#!/usr/bin/env node
/**
 * Godot MCP Server - Modular Version
 *
 * This MCP server provides tools for interacting with the Godot game engine.
 * It enables AI assistants to launch the Godot editor, run Godot projects,
 * capture debug output, and control project execution.
 *
 * This is the refactored modular version with improved architecture.
 */

import { GodotMCPServer } from './core/server.js';
import { Logger } from './utils/logger.js';

const logger = Logger.createScopedLogger('Main');

/**
 * Main function to start the Godot MCP server
 */
async function main(): Promise<void> {
  try {
    // 检查是否使用简化接口
    const useSimplifiedInterface = process.env.USE_SIMPLIFIED_INTERFACE === 'true';

    logger.info('🚀 Initializing Godot MCP Server...');
    logger.info(`📋 Interface Mode: ${useSimplifiedInterface ? 'Simplified (3 tools)' : 'Traditional (18 tools)'}`);

    if (useSimplifiedInterface) {
      logger.info('🤖 AI-Enhanced Mode: Enabled');
    }

    // Create and start the server
    const server = new GodotMCPServer({ useSimplifiedInterface });
    await server.start();

    // Log successful startup
    logger.info('✅ Godot MCP Server is now running on stdio');

    // The server will continue running until terminated

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    logger.error(`❌ Failed to start Godot MCP Server: ${errorMessage}`);

    if (error instanceof Error && error.stack) {
      logger.debug(`Stack trace: ${error.stack}`);
    }

    process.exit(1);
  }
}

/**
 * Handle unhandled promise rejections
 */
process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled promise rejection:', reason);
  logger.debug('Promise:', promise);
  process.exit(1);
});

/**
 * Handle uncaught exceptions
 */
process.on('uncaughtException', (error) => {
  logger.error('Uncaught exception:', error);
  process.exit(1);
});

// Start the server
main().catch((error) => {
  const errorMessage = error instanceof Error ? error.message : 'Unknown error';
  console.error(`Fatal error: ${errorMessage}`);
  process.exit(1);
});
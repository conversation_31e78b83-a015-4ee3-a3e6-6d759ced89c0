#!/usr/bin/env -S godot --headless --script
# GDScript Scene Operations Module
# Handles all scene-related operations: create, modify, save

class_name GodotSceneOperations

# Create a new scene with specified root node type
static func create_scene(params: Dictionary) -> bool:
	# Validate required parameters
	if not GodotArgumentParser.validate_required_params(params, ["scene_path"]):
		return false
	
	var scene_path = params.scene_path
	GodotLogger.log_info("Creating scene: " + scene_path)
	GodotLogger.log_debug_environment()
	
	# Normalize scene path
	var full_scene_path = GodotArgumentParser.normalize_scene_path(scene_path)
	GodotLogger.log_debug("Scene path (with res://): " + full_scene_path)
	
	var absolute_scene_path = GodotArgumentParser.get_absolute_path(full_scene_path)
	GodotLogger.log_debug("Absolute scene path: " + absolute_scene_path)
	
	# Get root node type (default to Node2D)
	var root_node_type = params.get("root_node_type", "Node2D")
	GodotLogger.log_debug("Root node type: " + root_node_type)
	
	# Create the root node
	var scene_root = GodotClassUtils.instantiate_class(root_node_type)
	if not scene_root:
		GodotLogger.log_error("Failed to instantiate node of type: " + root_node_type)
		GodotLogger.log_error("Make sure the class exists and can be instantiated")
		return false
	
	scene_root.name = "root"
	scene_root.owner = scene_root
	GodotLogger.log_debug("Root node created with name: " + scene_root.name)
	
	# Pack the scene
	var packed_scene = PackedScene.new()
	var pack_result = packed_scene.pack(scene_root)
	GodotLogger.log_debug("Pack result: " + str(pack_result) + " (OK=" + str(OK) + ")")
	
	if pack_result != OK:
		GodotLogger.log_error("Failed to pack scene: " + str(pack_result))
		return false
	
	# Ensure scene directory exists
	var scene_dir = full_scene_path.get_base_dir()
	if not _ensure_scene_directory_exists(scene_dir):
		return false
	
	# Save the scene
	GodotLogger.log_debug("Saving scene to: " + full_scene_path)
	var save_error = ResourceSaver.save(packed_scene, full_scene_path)
	GodotLogger.log_debug("Save result: " + str(save_error) + " (OK=" + str(OK) + ")")
	
	if save_error == OK:
		# Verify the scene was created
		if _verify_scene_created(full_scene_path, absolute_scene_path):
			GodotLogger.log_success("Scene created successfully at: " + scene_path)
			return true
		else:
			GodotLogger.log_error("Scene file verification failed")
			return false
	else:
		_handle_save_error(save_error)
		return false

# Add a node to an existing scene
static func add_node(params: Dictionary) -> bool:
	# Validate required parameters
	if not GodotArgumentParser.validate_required_params(params, ["scene_path", "node_type", "node_name"]):
		return false
	
	var scene_path = params.scene_path
	var node_type = params.node_type
	var node_name = params.node_name
	var parent_node_path = params.get("parent_node_path", "root")
	
	GodotLogger.log_info("Adding node to scene: " + scene_path)
	GodotLogger.log_debug("Node type: " + node_type + ", Name: " + node_name + ", Parent: " + parent_node_path)
	
	# Normalize and validate scene path
	var full_scene_path = GodotArgumentParser.normalize_scene_path(scene_path)
	if not GodotArgumentParser.validate_file_exists(full_scene_path, "scene"):
		return false
	
	# Load the scene
	var scene = load(full_scene_path)
	if not scene:
		GodotLogger.log_error("Failed to load scene: " + full_scene_path)
		return false
	
	# Instantiate the scene
	var scene_instance = scene.instantiate()
	if not scene_instance:
		GodotLogger.log_error("Failed to instantiate scene")
		return false
	
	# Find parent node
	var parent_node = _find_node_by_path(scene_instance, parent_node_path)
	if not parent_node:
		GodotLogger.log_error("Parent node not found: " + parent_node_path)
		return false
	
	# Create the new node
	var new_node = GodotClassUtils.instantiate_class(node_type)
	if not new_node:
		GodotLogger.log_error("Failed to create node of type: " + node_type)
		return false
	
	new_node.name = node_name
	new_node.owner = scene_instance
	
	# Set properties if provided
	if params.has("properties") and params.properties is Dictionary:
		_apply_node_properties(new_node, params.properties)
	
	# Add node to parent
	parent_node.add_child(new_node)
	GodotLogger.log_debug("Node added to parent: " + parent_node.name)
	
	# Save the modified scene
	return _save_scene_instance(scene_instance, full_scene_path)

# Load a sprite into a Sprite2D node
static func load_sprite(params: Dictionary) -> bool:
	# Validate required parameters
	if not GodotArgumentParser.validate_required_params(params, ["scene_path", "node_path", "texture_path"]):
		return false
	
	var scene_path = params.scene_path
	var node_path = params.node_path
	var texture_path = params.texture_path
	
	GodotLogger.log_info("Loading sprite into scene: " + scene_path)
	GodotLogger.log_debug("Node path: " + node_path + ", Texture: " + texture_path)
	
	# Normalize paths
	var full_scene_path = GodotArgumentParser.normalize_scene_path(scene_path)
	var full_texture_path = GodotArgumentParser.normalize_scene_path(texture_path)
	
	# Validate files exist
	if not GodotArgumentParser.validate_file_exists(full_scene_path, "scene"):
		return false
	if not GodotArgumentParser.validate_file_exists(full_texture_path, "texture"):
		return false
	
	# Load scene and texture
	var scene = load(full_scene_path)
	var texture = load(full_texture_path)
	
	if not scene:
		GodotLogger.log_error("Failed to load scene: " + full_scene_path)
		return false
	if not texture:
		GodotLogger.log_error("Failed to load texture: " + full_texture_path)
		return false
	
	# Instantiate scene and find sprite node
	var scene_instance = scene.instantiate()
	var sprite_node = _find_node_by_path(scene_instance, node_path)
	
	if not sprite_node:
		GodotLogger.log_error("Sprite node not found: " + node_path)
		return false
	
	if not sprite_node is Sprite2D:
		GodotLogger.log_error("Node is not a Sprite2D: " + node_path)
		return false
	
	# Set the texture
	sprite_node.texture = texture
	GodotLogger.log_debug("Texture loaded into sprite node")
	
	# Save the modified scene
	return _save_scene_instance(scene_instance, full_scene_path)

# Save a scene (with optional new path)
static func save_scene(params: Dictionary) -> bool:
	# Validate required parameters
	if not GodotArgumentParser.validate_required_params(params, ["scene_path"]):
		return false
	
	var scene_path = params.scene_path
	var new_path = params.get("new_path", "")
	
	GodotLogger.log_info("Saving scene: " + scene_path)
	
	# Normalize scene path
	var full_scene_path = GodotArgumentParser.normalize_scene_path(scene_path)
	if not GodotArgumentParser.validate_file_exists(full_scene_path, "scene"):
		return false
	
	# Load the scene
	var scene = load(full_scene_path)
	if not scene:
		GodotLogger.log_error("Failed to load scene: " + full_scene_path)
		return false
	
	# Determine save path
	var save_path = full_scene_path
	if not new_path.is_empty():
		save_path = GodotArgumentParser.normalize_scene_path(new_path)
		GodotLogger.log_debug("Saving to new path: " + save_path)
	
	# Ensure directory exists
	var scene_dir = save_path.get_base_dir()
	if not _ensure_scene_directory_exists(scene_dir):
		return false
	
	# Save the scene
	var save_error = ResourceSaver.save(scene, save_path)
	if save_error == OK:
		GodotLogger.log_success("Scene saved successfully to: " + save_path)
		return true
	else:
		_handle_save_error(save_error)
		return false

# Private helper functions

# Ensure scene directory exists
static func _ensure_scene_directory_exists(scene_dir: String) -> bool:
	if scene_dir == "res://":
		return true  # Root directory always exists

	var scene_dir_relative = scene_dir.substr(6)  # Remove "res://" prefix
	GodotLogger.log_debug("Ensuring scene directory exists: " + scene_dir_relative)

	var absolute_dir = GodotArgumentParser.get_absolute_path(scene_dir)

	# Check if directory exists
	if DirAccess.dir_exists_absolute(absolute_dir):
		GodotLogger.log_debug("Directory already exists: " + absolute_dir)
		return true

	# Create directory
	GodotLogger.log_debug("Creating directory: " + scene_dir_relative)
	var dir = DirAccess.open("res://")
	if not dir:
		GodotLogger.log_error("Failed to open res:// directory")
		return false

	var make_dir_error = dir.make_dir_recursive(scene_dir_relative)
	if make_dir_error == OK:
		GodotLogger.log_debug("Directory created successfully")
		return true
	else:
		GodotLogger.log_error("Failed to create directory: " + str(make_dir_error))
		return false

# Verify scene was created successfully
static func _verify_scene_created(resource_path: String, absolute_path: String) -> bool:
	# In debug mode, do extensive verification
	if GodotLogger.debug_mode:
		OS.delay_msec(500)  # Wait for file system

		var file_check_abs = FileAccess.file_exists(absolute_path)
		var file_check_res = FileAccess.file_exists(resource_path)
		var res_exists = ResourceLoader.exists(resource_path)

		GodotLogger.log_debug("File exists (absolute): " + str(file_check_abs))
		GodotLogger.log_debug("File exists (resource): " + str(file_check_res))
		GodotLogger.log_debug("Resource exists: " + str(res_exists))

		if file_check_abs or file_check_res or res_exists:
			# Try to load the scene to verify it's valid
			var test_load = ResourceLoader.load(resource_path)
			if test_load:
				GodotLogger.log_debug("Scene file can be loaded correctly")
				return true
			else:
				GodotLogger.log_warn("Scene file exists but cannot be loaded")
				return false
		else:
			GodotLogger.log_error("All file existence checks failed")
			return false
	else:
		# Simple check in non-debug mode
		return FileAccess.file_exists(resource_path)

# Handle save errors with detailed messages
static func _handle_save_error(save_error: int):
	var error_message = "Failed to save scene. Error code: " + str(save_error)

	match save_error:
		ERR_CANT_CREATE:
			error_message += " (ERR_CANT_CREATE - Cannot create the scene file)"
		ERR_CANT_OPEN:
			error_message += " (ERR_CANT_OPEN - Cannot open the scene file for writing)"
		ERR_FILE_CANT_WRITE:
			error_message += " (ERR_FILE_CANT_WRITE - Cannot write to the scene file)"
		ERR_FILE_NO_PERMISSION:
			error_message += " (ERR_FILE_NO_PERMISSION - No permission to write the scene file)"

	GodotLogger.log_error(error_message)

# Find node by path in scene tree
static func _find_node_by_path(root_node: Node, node_path: String) -> Node:
	if node_path == "root":
		return root_node

	var path_parts = node_path.split("/")
	var current_node = root_node

	for i in range(path_parts.size()):
		var part = path_parts[i]
		if part == "root":
			continue  # Skip root part

		var found = false
		for child in current_node.get_children():
			if child.name == part:
				current_node = child
				found = true
				break

		if not found:
			GodotLogger.log_error("Node not found in path: " + part)
			return null

	return current_node

# Apply properties to a node
static func _apply_node_properties(node: Node, properties: Dictionary):
	for property_name in properties:
		var property_value = properties[property_name]

		if node.has_method("set_" + property_name):
			node.call("set_" + property_name, property_value)
			GodotLogger.log_debug("Set property " + property_name + " = " + str(property_value))
		elif property_name in node:
			node.set(property_name, property_value)
			GodotLogger.log_debug("Set property " + property_name + " = " + str(property_value))
		else:
			GodotLogger.log_warn("Property not found on node: " + property_name)

# Save a scene instance to file
static func _save_scene_instance(scene_instance: Node, scene_path: String) -> bool:
	var packed_scene = PackedScene.new()
	var pack_result = packed_scene.pack(scene_instance)

	if pack_result != OK:
		GodotLogger.log_error("Failed to pack scene: " + str(pack_result))
		return false

	var save_error = ResourceSaver.save(packed_scene, scene_path)
	if save_error == OK:
		GodotLogger.log_success("Scene saved successfully")
		return true
	else:
		_handle_save_error(save_error)
		return false

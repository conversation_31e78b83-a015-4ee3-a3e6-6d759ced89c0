#!/usr/bin/env node
/**
 * 测试智能代理启动和通信
 */

import { PersistentGodotManager } from './build/agent/persistent-godot-manager.js';
import { GodotPathDetector } from './build/godot/path-detector.js';

async function testIntelligentAgent() {
  try {
    console.log('🤖 测试智能代理启动和通信...\n');
    
    // 1. 初始化路径检测器
    console.log('1. 初始化Godot路径检测器...');
    const pathDetector = new GodotPathDetector();
    const godotPath = await pathDetector.detectGodotPath();
    console.log(`   ✅ Godot路径: ${godotPath}`);
    
    // 2. 创建持久化代理管理器
    console.log('\n2. 创建持久化代理管理器...');
    const agentManager = new PersistentGodotManager(pathDetector);
    console.log('   ✅ 代理管理器创建成功');
    
    // 3. 测试项目路径
    const projectPath = "E:\\Github\\4.2.1\\Game-6";
    console.log(`\n3. 验证项目路径: ${projectPath}`);
    
    const fs = await import('fs');
    const path = await import('path');
    
    if (!fs.existsSync(projectPath)) {
      throw new Error(`项目路径不存在: ${projectPath}`);
    }
    
    const projectFile = path.join(projectPath, 'project.godot');
    if (!fs.existsSync(projectFile)) {
      throw new Error(`project.godot文件不存在: ${projectFile}`);
    }
    
    console.log('   ✅ 项目路径验证通过');
    
    // 4. 启动智能代理
    console.log('\n4. 启动智能代理...');
    console.log('   ⏳ 正在启动Godot代理进程 (最多30秒)...');
    
    const startTime = Date.now();
    await agentManager.startAgent(projectPath);
    const startDuration = Date.now() - startTime;
    
    console.log(`   ✅ 智能代理启动成功! (耗时: ${startDuration}ms)`);
    
    // 5. 测试基本通信
    console.log('\n5. 测试基本通信...');
    
    const pingResponse = await agentManager.sendCommand({
      action: 'ping'
    });
    
    console.log('   ✅ Ping测试成功:', pingResponse);
    
    // 6. 测试项目信息查询
    console.log('\n6. 测试项目信息查询...');
    
    const projectInfoResponse = await agentManager.sendCommand({
      action: 'get_project_info'
    });

    console.log('   ✅ 项目信息查询成功:');
    console.log(`      项目名称: ${projectInfoResponse.data.name}`);
    console.log(`      项目版本: ${projectInfoResponse.data.version}`);
    console.log(`      Godot版本: ${JSON.stringify(projectInfoResponse.data.godot_version)}`);

    // 7. 测试场景查询
    console.log('\n7. 测试场景查询...');

    const sceneQueryResponse = await agentManager.sendCommand({
      action: 'query_scene',
      scenePath: 'res://Scenes/Game/Island/IslandBase.tscn'
    });

    if (sceneQueryResponse.success) {
      console.log('   ✅ 场景查询成功:');
      console.log(`      场景路径: ${sceneQueryResponse.data.scenePath}`);
      console.log(`      根节点: ${sceneQueryResponse.data.sceneInfo.name} (${sceneQueryResponse.data.sceneInfo.type})`);
      console.log(`      子节点数量: ${sceneQueryResponse.data.sceneInfo.children.length}`);
    } else {
      console.log('   ⚠️  场景查询失败:', sceneQueryResponse.error);
    }

    // 8. 测试场景列表
    console.log('\n8. 测试场景列表查询...');

    const scenesListResponse = await agentManager.sendCommand({
      action: 'list_scenes'
    });
    
    if (scenesListResponse.success) {
      console.log(`   ✅ 找到 ${scenesListResponse.data.scenes.length} 个场景文件:`);
      scenesListResponse.data.scenes.slice(0, 5).forEach((scene, index) => {
        console.log(`      ${index + 1}. ${scene}`);
      });
      if (scenesListResponse.data.scenes.length > 5) {
        console.log(`      ... 还有 ${scenesListResponse.data.scenes.length - 5} 个场景`);
      }
    } else {
      console.log('   ⚠️  场景列表查询失败:', scenesListResponse.error);
    }
    
    // 9. 停止代理
    console.log('\n9. 停止智能代理...');
    await agentManager.stopAgent();
    console.log('   ✅ 智能代理已停止');
    
    console.log('\n🎉 智能代理测试完成! 所有功能正常工作。');
    
    // 10. 总结
    console.log('\n📊 测试总结:');
    console.log(`   ✅ 代理启动时间: ${startDuration}ms`);
    console.log('   ✅ 基本通信: 正常');
    console.log('   ✅ 项目信息查询: 正常');
    console.log('   ✅ 场景查询: 正常');
    console.log('   ✅ 场景列表: 正常');
    
    console.log('\n🚀 现在您可以在MCP客户端中使用intelligent_scene_operation工具了!');
    
  } catch (error) {
    console.error('\n❌ 智能代理测试失败:', error.message);
    console.error('错误详情:', error.stack);
    
    console.log('\n🔧 故障排除建议:');
    console.log('1. 检查Godot路径是否正确');
    console.log('2. 确保项目路径存在且包含project.godot文件');
    console.log('3. 检查Godot是否可以正常运行');
    console.log('4. 查看上面的详细错误信息');
    console.log('5. 确保没有其他Godot进程在运行');
    
    process.exit(1);
  }
}

// 运行测试
testIntelligentAgent().catch(console.error);

/**
 * AI任务规划器
 * 使用AI模型理解用户请求并生成Godot操作序列
 */

import { AIClient, AIMessage, AIFunction } from './ai-client.js';
import { AIConfigManager } from './ai-config.js';
import { Logger } from '../utils/logger.js';
import { OperationParams } from '../core/types.js';
import * as fs from 'fs';
import * as path from 'path';

const logger = Logger.createScopedLogger('TaskPlanner');

export interface TaskStep {
  action: string;
  parameters: OperationParams;
  reason: string;
  priority: number;
}

export interface TaskPlan {
  task_type: 'scene_operation' | 'project_query' | 'project_management' | 'debug_run';
  description: string;
  steps: TaskStep[];
  expected_outcome: string;
  risks: string[];
  estimated_duration: number;
}

export interface PlanningContext {
  projectPath: string;
  currentScenes: string[];
  recentOperations: string[];
  userPreferences?: Record<string, any>;
}

/**
 * AI任务规划器
 */
export class TaskPlanner {
  private aiClient: AIClient;
  private configManager: AIConfigManager;
  private systemPrompt: string = '';
  private conversationHistory: AIMessage[] = [];

  constructor() {
    this.aiClient = new AIClient();
    this.configManager = AIConfigManager.getInstance();
    this.loadSystemPrompt();
  }

  /**
   * 加载系统提示词
   */
  private loadSystemPrompt(): void {
    try {
      const config = this.configManager.getAgentConfig();
      const promptPath = path.resolve(config.systemPromptFile);
      
      if (fs.existsSync(promptPath)) {
        this.systemPrompt = fs.readFileSync(promptPath, 'utf-8');
        logger.debug('系统提示词加载成功');
      } else {
        logger.warn(`系统提示词文件不存在: ${promptPath}`);
        this.systemPrompt = this.getDefaultSystemPrompt();
      }
    } catch (error) {
      logger.error('加载系统提示词失败:', error);
      this.systemPrompt = this.getDefaultSystemPrompt();
    }
  }

  /**
   * 获取默认系统提示词
   */
  private getDefaultSystemPrompt(): string {
    return `你是一个专业的Godot游戏引擎智能助手，负责理解用户的自然语言请求并将其转换为具体的Godot操作序列。

请根据用户的请求生成详细的任务计划，包括：
1. 任务类型（scene_operation、project_query、project_management、debug_run）
2. 具体的操作步骤
3. 每个步骤的参数和原因
4. 预期结果和潜在风险

始终优先考虑安全性和用户体验。`;
  }

  /**
   * 规划任务
   */
  public async planTask(
    userRequest: string, 
    context: PlanningContext
  ): Promise<TaskPlan> {
    try {
      logger.info(`开始规划任务: ${userRequest}`);

      // 构建上下文信息
      const contextInfo = this.buildContextInfo(context);
      
      // 构建消息
      const messages: AIMessage[] = [
        { role: 'system', content: this.systemPrompt },
        { role: 'system', content: `当前上下文信息：\n${contextInfo}` },
        ...this.getRecentHistory(),
        { role: 'user', content: userRequest }
      ];

      // 定义可用的函数
      const functions = this.getAvailableFunctions();

      // 调用AI模型
      const result = await this.aiClient.completeWithFunctions(messages, functions);

      // 解析结果
      let taskPlan: TaskPlan;
      
      if (result.function_call) {
        taskPlan = this.parseFunctionCall(result.function_call);
      } else {
        taskPlan = this.parseTextResponse(result.content);
      }

      // 验证和优化任务计划
      taskPlan = this.validateAndOptimizePlan(taskPlan, context);

      // 更新对话历史
      this.updateConversationHistory(userRequest, taskPlan);

      logger.info(`任务规划完成: ${taskPlan.steps.length}个步骤`);
      return taskPlan;

    } catch (error) {
      logger.error('任务规划失败:', error);
      throw new Error(`任务规划失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 构建上下文信息
   */
  private buildContextInfo(context: PlanningContext): string {
    const info = [
      `项目路径: ${context.projectPath}`,
      `当前场景: ${context.currentScenes.length > 0 ? context.currentScenes.join(', ') : '无'}`,
      `最近操作: ${context.recentOperations.length > 0 ? context.recentOperations.slice(-3).join(', ') : '无'}`
    ];

    if (context.userPreferences) {
      info.push(`用户偏好: ${JSON.stringify(context.userPreferences)}`);
    }

    return info.join('\n');
  }

  /**
   * 获取可用的函数定义
   */
  private getAvailableFunctions(): AIFunction[] {
    return [
      {
        name: 'create_task_plan',
        description: '创建详细的Godot操作任务计划',
        parameters: {
          type: 'object',
          properties: {
            task_type: {
              type: 'string',
              enum: ['scene_operation', 'project_query', 'project_management', 'debug_run'],
              description: '任务类型'
            },
            description: {
              type: 'string',
              description: '任务描述'
            },
            steps: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  action: { type: 'string', description: '具体操作' },
                  parameters: { type: 'object', description: '操作参数' },
                  reason: { type: 'string', description: '执行原因' },
                  priority: { type: 'number', description: '优先级(0-10)' }
                },
                required: ['action', 'parameters', 'reason', 'priority']
              },
              description: '操作步骤列表'
            },
            expected_outcome: {
              type: 'string',
              description: '预期结果'
            },
            risks: {
              type: 'array',
              items: { type: 'string' },
              description: '潜在风险列表'
            },
            estimated_duration: {
              type: 'number',
              description: '预估执行时间(秒)'
            }
          },
          required: ['task_type', 'description', 'steps', 'expected_outcome']
        }
      }
    ];
  }

  /**
   * 解析函数调用结果
   */
  private parseFunctionCall(functionCall: { name: string; arguments: string }): TaskPlan {
    try {
      if (functionCall.name === 'create_task_plan') {
        const args = JSON.parse(functionCall.arguments);
        return {
          task_type: args.task_type,
          description: args.description,
          steps: args.steps || [],
          expected_outcome: args.expected_outcome,
          risks: args.risks || [],
          estimated_duration: args.estimated_duration || 60
        };
      }
    } catch (error) {
      logger.error('解析函数调用失败:', error);
    }

    throw new Error('无法解析AI响应');
  }

  /**
   * 解析文本响应
   */
  private parseTextResponse(content: string): TaskPlan {
    // 尝试从文本中提取JSON
    const jsonMatch = content.match(/```json\s*([\s\S]*?)\s*```/);
    if (jsonMatch) {
      try {
        return JSON.parse(jsonMatch[1]);
      } catch (error) {
        logger.error('解析JSON失败:', error);
      }
    }

    // 如果无法解析，创建一个基本的任务计划
    return {
      task_type: 'scene_operation',
      description: content.substring(0, 100),
      steps: [{
        action: 'manual_review',
        parameters: { content },
        reason: 'AI响应需要人工审核',
        priority: 1
      }],
      expected_outcome: '需要进一步分析',
      risks: ['AI响应解析失败'],
      estimated_duration: 30
    };
  }

  /**
   * 验证和优化任务计划
   */
  private validateAndOptimizePlan(plan: TaskPlan, context: PlanningContext): TaskPlan {
    // 验证步骤的有效性
    plan.steps = plan.steps.filter(step => {
      if (!step.action || !step.parameters) {
        logger.warn(`无效的步骤: ${JSON.stringify(step)}`);
        return false;
      }
      return true;
    });

    // 按优先级排序
    plan.steps.sort((a, b) => (a.priority || 5) - (b.priority || 5));

    // 添加安全检查步骤
    if (this.needsSafetyCheck(plan)) {
      plan.steps.unshift({
        action: 'safety_check',
        parameters: { projectPath: context.projectPath },
        reason: '执行前安全检查',
        priority: 0
      });
    }

    return plan;
  }

  /**
   * 检查是否需要安全检查
   */
  private needsSafetyCheck(plan: TaskPlan): boolean {
    const destructiveActions = ['delete', 'remove', 'clear', 'reset'];
    return plan.steps.some(step => 
      destructiveActions.some(action => 
        step.action.toLowerCase().includes(action)
      )
    );
  }

  /**
   * 获取最近的对话历史
   */
  private getRecentHistory(): AIMessage[] {
    const maxHistory = this.configManager.getAgentConfig().maxContextHistory;
    return this.conversationHistory.slice(-maxHistory);
  }

  /**
   * 更新对话历史
   */
  private updateConversationHistory(userRequest: string, taskPlan: TaskPlan): void {
    this.conversationHistory.push(
      { role: 'user', content: userRequest },
      { role: 'assistant', content: `已生成任务计划: ${taskPlan.description}` }
    );

    // 限制历史长度
    const maxHistory = this.configManager.getAgentConfig().maxContextHistory;
    if (this.conversationHistory.length > maxHistory) {
      this.conversationHistory = this.conversationHistory.slice(-maxHistory);
    }
  }

  /**
   * 清除对话历史
   */
  public clearHistory(): void {
    this.conversationHistory = [];
    logger.info('对话历史已清除');
  }

  /**
   * 获取对话历史
   */
  public getHistory(): AIMessage[] {
    return [...this.conversationHistory];
  }

  /**
   * 检查AI服务是否可用
   */
  public async checkAIHealth(): Promise<boolean> {
    return this.aiClient.checkHealth();
  }
}

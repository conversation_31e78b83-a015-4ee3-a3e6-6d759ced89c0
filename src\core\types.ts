/**
 * Core type definitions for Godot MCP Server
 */

/**
 * Interface representing a running Godot process
 */
export interface GodotProcess {
  process: any;
  output: string[];
  errors: string[];
}

/**
 * Interface for server configuration
 */
export interface GodotServerConfig {
  godotPath?: string;
  debugMode?: boolean;
  godotDebugMode?: boolean;
  strictPathValidation?: boolean;
}

/**
 * Interface for operation parameters
 */
export interface OperationParams {
  [key: string]: any;
}

/**
 * Interface for operation results
 */
export interface OperationResult {
  success: boolean;
  data?: any;
  error?: string;
  output?: string[];
}

/**
 * Interface for MCP tool definition
 */
export interface MCPTool {
  name: string;
  description: string;
  inputSchema: {
    type: string;
    properties: Record<string, any>;
    required: string[];
  };
  handler: (params: OperationParams) => Promise<OperationResult>;
}

/**
 * Interface for MCP server capabilities
 */
export interface ServerCapabilities {
  tools?: Record<string, any>;
  [key: string]: any;
}

/**
 * Interface for MCP server configuration
 */
export interface MCPServerConfig {
  name: string;
  version: string;
  capabilities: ServerCapabilities;
  [key: string]: any;
}

/**
 * Enum for log levels
 */
export enum LogLevel {
  DEBUG = 'DEBUG',
  INFO = 'INFO',
  WARN = 'WARN',
  ERROR = 'ERROR'
}

/**
 * Type for path validation cache
 */
export type PathValidationCache = Map<string, boolean>;

/**
 * Interface for Godot project information
 */
export interface GodotProjectInfo {
  name: string;
  path: string;
  version?: string;
  description?: string;
}

/**
 * Interface for scene creation parameters
 */
export interface SceneCreationParams {
  scenePath: string;
  rootNodeType?: string;
}

/**
 * Interface for node addition parameters
 */
export interface NodeAdditionParams {
  scenePath: string;
  nodeType: string;
  nodeName: string;
  parentNodePath?: string;
  properties?: Record<string, any>;
}

/**
 * Interface for sprite loading parameters
 */
export interface SpriteLoadingParams {
  scenePath: string;
  nodePath: string;
  texturePath: string;
}

/**
 * Interface for mesh library export parameters
 */
export interface MeshLibraryExportParams {
  scenePath: string;
  outputPath: string;
  meshItemNames?: string[];
}

/**
 * Interface for scene saving parameters
 */
export interface SceneSavingParams {
  scenePath: string;
  newPath?: string;
}

/**
 * Interface for UID retrieval parameters
 */
export interface UIDRetrievalParams {
  filePath: string;
}

/**
 * Type guard functions
 */
export function isGodotProcess(obj: any): obj is GodotProcess {
  return obj && 
         typeof obj.process !== 'undefined' &&
         Array.isArray(obj.output) &&
         Array.isArray(obj.errors);
}

export function isOperationResult(obj: any): obj is OperationResult {
  return obj && typeof obj.success === 'boolean';
}

/**
 * Constants
 */
export const DEFAULT_CONFIG: GodotServerConfig = {
  debugMode: false,
  godotDebugMode: true,
  strictPathValidation: false,
};

export const MCP_SERVER_INFO = {
  name: 'godot-mcp',
  version: '0.1.0',
} as const;

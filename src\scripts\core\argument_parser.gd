#!/usr/bin/env -S godot --headless --script
# GDScript Argument Parser Module
# Handles command line argument parsing and validation

class_name GodotArgumentParser

# Parsed argument data structure
class ParsedArgs:
	var operation: String
	var params: Dictionary
	var debug_mode: bool
	
	func _init(op: String, p: Dictionary, debug: bool):
		operation = op
		params = p
		debug_mode = debug

# Parse command line arguments and return ParsedArgs object
static func parse_arguments() -> ParsedArgs:
	var args = OS.get_cmdline_args()
	
	# Initialize debug mode first
	var debug_mode = "--debug-godot" in args
	GodotLogger.debug_mode = debug_mode
	GodotLogger.init_debug_mode()
	
	GodotLogger.log_debug_args()
	
	# Find the script argument and determine positions
	var script_index = args.find("--script")
	if script_index == -1:
		GodotLogger.log_error("Could not find --script argument")
		return null
	
	# The operation should be 2 positions after the script path
	var operation_index = script_index + 2
	# The params should be 3 positions after the script path
	var params_index = script_index + 3
	
	if args.size() <= params_index:
		GodotLogger.log_error("Usage: godot --headless --script godot_operations.gd <operation> <json_params>")
		GodotLogger.log_error("Not enough command-line arguments provided.")
		return null
	
	GodotLogger.log_debug("Script index: " + str(script_index))
	GodotLogger.log_debug("Operation index: " + str(operation_index))
	GodotLogger.log_debug("Params index: " + str(params_index))
	
	var operation = args[operation_index]
	var params_json = args[params_index]
	
	GodotLogger.log_info("Operation: " + operation)
	GodotLogger.log_debug("Params JSON: " + params_json)
	
	# Parse JSON parameters
	var params = parse_json_params(params_json)
	if params == null:
		return null
	
	return ParsedArgs.new(operation, params, debug_mode)

# Parse JSON parameters with error handling
static func parse_json_params(params_json: String) -> Dictionary:
	var json = JSON.new()
	var error = json.parse(params_json)
	
	if error == OK:
		var params = json.get_data()
		if params is Dictionary:
			return params
		else:
			GodotLogger.log_error("JSON parameters must be a dictionary/object")
			return {}
	else:
		GodotLogger.log_error("Failed to parse JSON parameters: " + params_json)
		GodotLogger.log_error("JSON Error: " + json.get_error_message() + " at line " + str(json.get_error_line()))
		return {}

# Validate required parameters for an operation
static func validate_required_params(params: Dictionary, required_keys: Array) -> bool:
	for key in required_keys:
		if not params.has(key):
			GodotLogger.log_error("Missing required parameter: " + key)
			return false
		if params[key] == null or (params[key] is String and params[key].is_empty()):
			GodotLogger.log_error("Parameter '" + key + "' cannot be empty")
			return false
	return true

# Normalize file paths (ensure they start with res://)
static func normalize_scene_path(path: String) -> String:
	if not path.begins_with("res://"):
		return "res://" + path
	return path

# Get absolute path from Godot resource path
static func get_absolute_path(resource_path: String) -> String:
	return ProjectSettings.globalize_path(resource_path)

# Validate that a file exists
static func validate_file_exists(path: String, file_type: String = "file") -> bool:
	var absolute_path = get_absolute_path(path)
	GodotLogger.log_debug("Checking if " + file_type + " exists: " + absolute_path)
	
	if not FileAccess.file_exists(absolute_path):
		GodotLogger.log_error(file_type.capitalize() + " does not exist at: " + absolute_path)
		return false
	
	return true

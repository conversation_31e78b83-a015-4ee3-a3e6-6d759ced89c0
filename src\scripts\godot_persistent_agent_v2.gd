#!/usr/bin/env -S godot --headless --script
# Godot Persistent Agent V2
# 持久化Godot代理，支持通过文件进行双向通信

extends SceneTree

# 文件路径
var command_file_path: String = ""
var response_file_path: String = ""

# 状态管理
var is_running: bool = false
var last_command_time: int = 0

# 日志系统
func log_info(message: String):
	print("[INFO] [PersistentAgent] ", message)

func log_error(message: String):
	print("[ERROR] [PersistentAgent] ", message)

func log_debug(message: String):
	print("[DEBUG] [PersistentAgent] ", message)

# 主入口函数
func _init():
	log_info("Godot Persistent Agent V2 starting...")
	
	# 解析命令行参数
	parse_command_line_args()
	
	# 验证文件路径
	if command_file_path.is_empty() or response_file_path.is_empty():
		log_error("Command file or response file path not provided")
		quit(1)
		return
	
	log_info("Command file: " + command_file_path)
	log_info("Response file: " + response_file_path)
	
	# 清理旧的响应文件
	cleanup_response_file()
	
	# 发送启动完成信号
	send_agent_started_response()
	
	# 开始主循环
	is_running = true
	log_info("Persistent agent started successfully")

# 解析命令行参数
func parse_command_line_args():
	var args = OS.get_cmdline_args()
	log_debug("Command line args: " + str(args))
	
	for i in range(args.size()):
		if args[i] == "--command-file" and i + 1 < args.size():
			command_file_path = args[i + 1]
		elif args[i] == "--response-file" and i + 1 < args.size():
			response_file_path = args[i + 1]

# 清理响应文件
func cleanup_response_file():
	if FileAccess.file_exists(response_file_path):
		var file = FileAccess.open(response_file_path, FileAccess.WRITE)
		if file:
			file.close()
			log_debug("Cleaned up response file")

# 发送代理启动完成响应
func send_agent_started_response():
	var response = {
		"type": "agent_started",
		"timestamp": Time.get_unix_time_from_system(),
		"message": "Godot persistent agent started successfully"
	}
	write_response(response)
	log_info("Sent agent_started response")

# 主处理循环
func _process(_delta):
	if not is_running:
		return
	
	# 检查命令文件
	check_for_commands()

# 检查命令文件
func check_for_commands():
	if not FileAccess.file_exists(command_file_path):
		return
	
	var file = FileAccess.open(command_file_path, FileAccess.READ)
	if not file:
		return
	
	var content = file.get_as_text()
	file.close()
	
	if content.is_empty():
		return
	
	# 解析命令
	var json = JSON.new()
	var parse_result = json.parse(content)
	
	if parse_result != OK:
		log_error("Failed to parse command JSON: " + content)
		return
	
	var command = json.data
	if not command.has("id") or not command.has("type"):
		log_error("Invalid command format: " + content)
		return
	
	log_info("Processing command: " + command.type)
	
	# 处理命令
	process_command(command)
	
	# 清理命令文件
	clear_command_file()

# 处理命令
func process_command(command: Dictionary):
	var response = {
		"id": command.id,
		"type": "response",
		"timestamp": Time.get_unix_time_from_system(),
		"success": false,
		"data": null,
		"error": null
	}
	
	match command.type:
		"query_scene":
			response = process_query_scene_command(command, response)
		"get_project_info":
			response = process_get_project_info_command(command, response)
		"list_scenes":
			response = process_list_scenes_command(command, response)
		"ping":
			response.success = true
			response.data = {"message": "pong", "agent_version": "v2"}
		_:
			response.error = "Unknown command type: " + command.type
	
	write_response(response)

# 处理查询场景命令
func process_query_scene_command(command: Dictionary, response: Dictionary) -> Dictionary:
	if not command.has("params") or not command.params.has("scenePath"):
		response.error = "Missing scenePath parameter"
		return response
	
	var scene_path = command.params.scenePath
	log_info("Querying scene: " + scene_path)
	
	# 检查场景文件是否存在
	if not FileAccess.file_exists(scene_path):
		response.error = "Scene file not found: " + scene_path
		return response
	
	# 加载场景
	var packed_scene = load(scene_path) as PackedScene
	if not packed_scene:
		response.error = "Failed to load scene: " + scene_path
		return response
	
	# 实例化场景以获取节点信息
	var scene_instance = packed_scene.instantiate()
	if not scene_instance:
		response.error = "Failed to instantiate scene: " + scene_path
		return response
	
	# 收集场景信息
	var scene_info = collect_scene_info(scene_instance)
	scene_instance.queue_free()
	
	response.success = true
	response.data = {
		"scenePath": scene_path,
		"sceneInfo": scene_info
	}
	
	return response

# 收集场景信息
func collect_scene_info(node: Node) -> Dictionary:
	var info = {
		"name": node.name,
		"type": node.get_class(),
		"children": [],
		"properties": {}
	}
	
	# 收集一些基本属性
	if node.has_method("get_position"):
		info.properties["position"] = str(node.get_position())
	if node.has_method("get_scale"):
		info.properties["scale"] = str(node.get_scale())
	if node.has_method("get_rotation"):
		info.properties["rotation"] = str(node.get_rotation())
	
	# 递归收集子节点信息
	for child in node.get_children():
		info.children.append(collect_scene_info(child))
	
	return info

# 处理获取项目信息命令
func process_get_project_info_command(command: Dictionary, response: Dictionary) -> Dictionary:
	var project_info = {
		"name": ProjectSettings.get_setting("application/config/name", "Unknown"),
		"version": ProjectSettings.get_setting("application/config/version", "1.0"),
		"godot_version": Engine.get_version_info(),
		"project_path": ProjectSettings.globalize_path("res://")
	}
	
	response.success = true
	response.data = project_info
	return response

# 处理列出场景命令
func process_list_scenes_command(command: Dictionary, response: Dictionary) -> Dictionary:
	var scenes = []
	var dir = DirAccess.open("res://")
	
	if dir:
		_scan_directory_for_scenes(dir, "res://", scenes)
	
	response.success = true
	response.data = {"scenes": scenes}
	return response

# 扫描目录查找场景文件
func _scan_directory_for_scenes(dir: DirAccess, path: String, scenes: Array):
	dir.list_dir_begin()
	var file_name = dir.get_next()
	
	while file_name != "":
		var full_path = path + "/" + file_name if path != "res://" else path + file_name
		
		if dir.current_is_dir() and not file_name.begins_with("."):
			var sub_dir = DirAccess.open(full_path)
			if sub_dir:
				_scan_directory_for_scenes(sub_dir, full_path, scenes)
		elif file_name.ends_with(".tscn"):
			scenes.append(full_path)
		
		file_name = dir.get_next()

# 写入响应
func write_response(response: Dictionary):
	var json_string = JSON.stringify(response)
	var file = FileAccess.open(response_file_path, FileAccess.WRITE)
	
	if file:
		file.store_string(json_string)
		file.close()
		log_debug("Response written: " + response.type)
	else:
		log_error("Failed to write response file")

# 清理命令文件
func clear_command_file():
	var file = FileAccess.open(command_file_path, FileAccess.WRITE)
	if file:
		file.close()

# 退出处理 - 简化版本，避免版本兼容性问题
func _ready():
	# 设置退出信号处理
	set_auto_accept_quit(true)

/**
 * 测试持久化Godot管理器
 */

import { PersistentGodotManager } from './persistent-godot-manager.js';
import { GodotPathDetector } from '../godot/path-detector.js';
import { Logger } from '../utils/logger.js';

const logger = Logger.createScopedLogger('TestPersistentManager');

async function testPersistentGodotManager() {
  logger.info('开始测试持久化Godot管理器...');

  try {
    // 创建路径检测器和管理器
    const pathDetector = new GodotPathDetector();
    const manager = new PersistentGodotManager(pathDetector);

    // 监听响应事件
    manager.on('response', (response) => {
      logger.info('收到响应:', response);
    });

    manager.on('stopped', () => {
      logger.info('Godot代理已停止');
    });

    // 启动代理
    const projectPath = process.cwd(); // 当前目录作为项目路径
    await manager.startAgent(projectPath);

    // 测试ping命令
    logger.info('发送ping命令...');
    const pingResponse = await manager.sendCommand({ action: 'ping' });
    logger.info('Ping响应:', pingResponse);

    // 测试加载项目
    logger.info('发送加载项目命令...');
    const loadResponse = await manager.sendCommand({
      action: 'load_project',
      project_path: projectPath
    });
    logger.info('加载项目响应:', loadResponse);

    // 测试创建场景
    logger.info('发送创建场景命令...');
    const createResponse = await manager.sendCommand({
      action: 'create_scene',
      scene_path: 'test_agent_scene.tscn',
      root_type: 'Node2D'
    });
    logger.info('创建场景响应:', createResponse);

    // 等待一段时间
    await new Promise(resolve => setTimeout(resolve, 2000));

    // 停止代理
    logger.info('停止代理...');
    await manager.stopAgent();

    logger.info('测试完成！');

  } catch (error) {
    logger.error('测试失败:', error);
  }
}

// 如果直接运行此文件，执行测试
if (import.meta.url === `file://${process.argv[1]}`) {
  testPersistentGodotManager().catch(console.error);
}
